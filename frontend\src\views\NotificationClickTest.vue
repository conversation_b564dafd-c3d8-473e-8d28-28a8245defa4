<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- 顶部导航栏 -->
    <nav class="glass border-b border-white/30 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <RouterLink to="/" class="text-xl font-bold text-gradient">
            1v1 Chat
          </RouterLink>
          <span class="text-sm text-gray-600">通知点击测试</span>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- 通知图标 -->
          <NotificationIcon />
          
          <RouterLink to="/" class="btn-secondary text-sm">
            返回首页
          </RouterLink>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto p-6">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">通知点击删除功能测试</h1>
        
        <!-- 说明 -->
        <div class="mb-6 p-4 bg-blue-50 rounded-lg">
          <h2 class="text-lg font-semibold text-blue-900 mb-2">测试说明</h2>
          <ul class="text-blue-800 space-y-1">
            <li>• 点击通知图标查看通知列表</li>
            <li>• 点击任何通知项目，该通知应该被删除</li>
            <li>• 点击好友请求，该请求应该被删除并跳转到好友页面</li>
            <li>• 通知图标的红点数量应该实时更新</li>
          </ul>
        </div>

        <!-- 测试按钮 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">添加测试好友请求</h3>
            <button 
              @click="addTestFriendRequest"
              class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              添加好友请求
            </button>
            <p class="text-xs text-gray-500 mt-2">
              添加一个测试好友请求到通知列表
            </p>
          </div>

          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">添加系统通知</h3>
            <button 
              @click="addTestNotification"
              class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              添加系统通知
            </button>
            <p class="text-xs text-gray-500 mt-2">
              添加一个测试系统通知到通知列表
            </p>
          </div>

          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">模拟接受好友请求</h3>
            <button 
              @click="simulateAcceptRequest"
              class="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
            >
              接受最新请求
            </button>
            <p class="text-xs text-gray-500 mt-2">
              模拟接受最新的好友请求
            </p>
          </div>

          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">清空所有通知</h3>
            <button 
              @click="clearAllNotifications"
              class="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            >
              清空通知
            </button>
            <p class="text-xs text-gray-500 mt-2">
              清空所有通知和好友请求
            </p>
          </div>
        </div>

        <!-- 当前状态 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h3 class="font-semibold text-gray-900 mb-3">当前通知状态</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-gray-600">好友请求:</span>
              <span class="font-medium ml-1">{{ notificationsStore.friendRequests.length }}</span>
            </div>
            <div>
              <span class="text-gray-600">系统通知:</span>
              <span class="font-medium ml-1">{{ notificationsStore.notifications.length }}</span>
            </div>
            <div>
              <span class="text-gray-600">未读通知:</span>
              <span class="font-medium ml-1">{{ notificationsStore.unreadCount }}</span>
            </div>
            <div>
              <span class="text-gray-600">总未读:</span>
              <span class="font-medium ml-1">{{ notificationsStore.totalUnreadCount }}</span>
            </div>
          </div>
        </div>

        <!-- 通知列表预览 -->
        <div class="mt-6 bg-gray-50 rounded-lg p-4">
          <h3 class="font-semibold text-gray-900 mb-3">当前通知列表</h3>
          
          <!-- 好友请求 -->
          <div v-if="notificationsStore.friendRequests.length > 0" class="mb-4">
            <h4 class="text-sm font-medium text-gray-700 mb-2">好友请求 ({{ notificationsStore.friendRequests.length }})</h4>
            <div class="space-y-2">
              <div 
                v-for="request in notificationsStore.friendRequests" 
                :key="request.id"
                class="bg-white p-3 rounded border text-sm"
              >
                <div class="flex justify-between items-center">
                  <span>{{ request.sender_username }} 申请添加您为好友</span>
                  <span class="text-xs text-gray-500">{{ request.status }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 系统通知 -->
          <div v-if="notificationsStore.notifications.length > 0">
            <h4 class="text-sm font-medium text-gray-700 mb-2">系统通知 ({{ notificationsStore.notifications.length }})</h4>
            <div class="space-y-2">
              <div 
                v-for="notification in notificationsStore.notifications" 
                :key="notification.id"
                class="bg-white p-3 rounded border text-sm"
                :class="{ 'opacity-60': notification.read }"
              >
                <div class="flex justify-between items-center">
                  <div>
                    <div class="font-medium">{{ notification.title }}</div>
                    <div class="text-gray-600">{{ notification.message }}</div>
                  </div>
                  <span class="text-xs text-gray-500">
                    {{ notification.read ? '已读' : '未读' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div v-if="notificationsStore.friendRequests.length === 0 && notificationsStore.notifications.length === 0" class="text-center text-gray-500 py-4">
            暂无通知
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useNotificationsStore } from '@/stores/notifications'
import NotificationIcon from '@/components/NotificationIcon.vue'

const notificationsStore = useNotificationsStore()

let requestCounter = 1
let notificationCounter = 1

const addTestFriendRequest = () => {
  const testRequest = {
    id: Date.now() + requestCounter,
    sender_id: 1000 + requestCounter,
    sender_username: `测试用户${requestCounter}`,
    sender_display_name: `测试用户${requestCounter}`,
    receiver_id: 1,
    receiver_username: '当前用户',
    receiver_display_name: '当前用户',
    status: 'pending' as const,
    message: `这是第${requestCounter}个测试好友请求`,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  
  notificationsStore.addFriendRequest(testRequest)
  requestCounter++
}

const addTestNotification = () => {
  notificationsStore.addNotification({
    type: 'system',
    title: `系统通知 ${notificationCounter}`,
    message: `这是第${notificationCounter}个测试系统通知，点击应该删除此通知。`,
    data: { test: true }
  })
  notificationCounter++
}

const simulateAcceptRequest = () => {
  const requests = notificationsStore.friendRequests
  if (requests.length > 0) {
    const latestRequest = requests[requests.length - 1]
    // 模拟接受好友请求的Socket事件
    const acceptedRequest = {
      ...latestRequest,
      status: 'accepted' as const,
      updated_at: new Date().toISOString()
    }
    
    // 直接调用removeFriendRequest来模拟Socket事件的处理
    notificationsStore.removeFriendRequest(latestRequest.id)
    console.log(`模拟接受好友请求: ${latestRequest.sender_username}`)
  }
}

const clearAllNotifications = () => {
  notificationsStore.clearAllNotifications()
  notificationsStore.clearFriendRequests()
}
</script>

<style scoped>
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.btn-secondary {
  @apply px-4 py-2 bg-white/80 text-gray-700 rounded-lg hover:bg-white/90 transition-colors border border-gray-200;
}
</style>
