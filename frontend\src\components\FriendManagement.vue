<template>
  <div class="space-y-6">
    <!-- 好友请求 -->
    <div class="glass rounded-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">好友请求</h3>
        <div class="flex space-x-2">
          <button
            v-for="type in requestTypes"
            :key="type.value"
            @click="requestType = type.value"
            :class="[
              'px-3 py-1 text-sm rounded-md transition-colors',
              requestType === type.value
                ? 'bg-primary-600 text-white'
                : 'bg-white/50 text-gray-700 hover:bg-white/70'
            ]"
          >
            {{ type.label }}
          </button>
        </div>
      </div>

      <div v-if="loadingRequests" class="text-center py-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
      </div>

      <div v-else-if="friendRequests.length > 0" class="space-y-3">
        <div
          v-for="request in friendRequests"
          :key="request.id"
          class="flex items-center justify-between p-4 bg-white/50 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
              <span class="text-sm font-medium text-primary-600">
                {{ getRequestUsername(request).charAt(0).toUpperCase() }}
              </span>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-900">{{ getRequestUsername(request) }}</div>
              <div v-if="request.message" class="text-sm text-gray-500 max-w-xs truncate">{{ request.message }}</div>
              <div class="text-xs text-gray-400">{{ formatTime(request.created_at) }}</div>
            </div>
          </div>
          
          <div class="flex space-x-2">
            <button
              v-if="requestType === 'received' && request.status === 'pending'"
              @click="acceptRequest(request.id)"
              class="btn-primary text-sm"
            >
              接受
            </button>
            <button
              v-if="requestType === 'received' && request.status === 'pending'"
              @click="declineRequest(request.id)"
              class="btn-secondary text-sm"
            >
              拒绝
            </button>
            <span
              v-else
              :class="[
                'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium',
                request.status === 'accepted' ? 'bg-green-100 text-green-800' :
                request.status === 'declined' ? 'bg-red-100 text-red-800' :
                'bg-yellow-100 text-yellow-800'
              ]"
            >
              {{ getStatusText(request.status) }}
            </span>
          </div>
        </div>
      </div>

      <div v-else class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无好友请求</h3>
        <p class="mt-1 text-sm text-gray-500">{{ getEmptyMessage() }}</p>
      </div>
    </div>

    <!-- 好友列表 -->
    <div class="glass rounded-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">好友列表</h3>
        <div class="flex items-center space-x-2">
          <button
            @click="testFriendRemoved"
            class="px-2 py-1 text-xs bg-red-100 text-red-600 rounded hover:bg-red-200"
            title="测试好友删除事件"
          >
            测试删除
          </button>
          <span class="text-sm text-gray-500">{{ friends.length }} 位好友</span>
        </div>
      </div>

      <div v-if="loadingFriends" class="text-center py-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
      </div>

      <div v-else-if="friends.length > 0" class="space-y-3">
        <div
          v-for="friend in friends"
          :key="friend.id"
          class="flex items-center justify-between p-4 bg-white/50 rounded-lg hover:bg-white/70 transition-colors"
        >
          <div class="flex items-center space-x-3">
            <div class="relative">
              <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                <span class="text-sm font-medium text-primary-600">
                  {{ friend.username.charAt(0).toUpperCase() }}
                </span>
              </div>
              <div 
                v-if="isOnline(friend.id)"
                class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"
              ></div>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-900">{{ friend.username }}</div>
              <div v-if="friend.display_name" class="text-sm text-gray-500">{{ friend.display_name }}</div>
              <div class="text-xs text-gray-400">最后活跃: {{ formatTime(friend.last_seen) }}</div>
            </div>
          </div>
          
          <div class="flex space-x-2">
            <button
              @click="startChat(friend)"
              class="btn-primary text-sm"
            >
              聊天
            </button>
            <button
              @click="removeFriend(friend.id)"
              class="btn-secondary text-sm text-red-600 hover:bg-red-50"
            >
              移除
            </button>
          </div>
        </div>
      </div>

      <div v-else class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无好友</h3>
        <p class="mt-1 text-sm text-gray-500">通过搜索用户来添加好友</p>
      </div>
    </div>

    <!-- 屏蔽列表 -->
    <div class="glass rounded-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">屏蔽列表</h3>
        <span class="text-sm text-gray-500">{{ blockedUsers.length }} 位用户</span>
      </div>

      <div v-if="loadingBlocked" class="text-center py-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
      </div>

      <div v-else-if="blockedUsers.length > 0" class="space-y-3">
        <div
          v-for="blocked in blockedUsers"
          :key="blocked.id"
          class="flex items-center justify-between p-4 bg-white/50 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
              <span class="text-sm font-medium text-gray-600">
                {{ blocked.blocked_username.charAt(0).toUpperCase() }}
              </span>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-900">{{ blocked.blocked_username }}</div>
              <div v-if="blocked.blocked_display_name" class="text-sm text-gray-500">{{ blocked.blocked_display_name }}</div>
              <div class="text-xs text-gray-400">屏蔽时间: {{ formatTime(blocked.created_at) }}</div>
            </div>
          </div>
          
          <button
            @click="unblockUser(blocked.blocked_id)"
            class="btn-secondary text-sm"
          >
            解除屏蔽
          </button>
        </div>
      </div>

      <div v-else class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无屏蔽用户</h3>
        <p class="mt-1 text-sm text-gray-500">您没有屏蔽任何用户</p>
      </div>
    </div>

    <!-- 被限制用户列表 -->
    <div class="glass rounded-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">被限制用户</h3>
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500">{{ restrictedUsers.length }} 位用户</span>
          <button
            @click="loadRestrictedUsers"
            class="px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
            title="刷新列表"
          >
            刷新
          </button>
        </div>
      </div>

      <div v-if="loadingRestricted" class="text-center py-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
      </div>

      <div v-else-if="restrictedUsers.length > 0" class="space-y-3">
        <div
          v-for="restrictedUser in restrictedUsers"
          :key="restrictedUser.user.id"
          class="flex items-center justify-between p-4 bg-white/50 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
              <span class="text-sm font-medium text-orange-600">
                {{ restrictedUser.user.username.charAt(0).toUpperCase() }}
              </span>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-900">{{ restrictedUser.user.username }}</div>
              <div class="text-xs text-gray-500">
                被拒绝 {{ restrictedUser.reject_count }} 次
                <span v-if="restrictedUser.last_rejected_at">
                  · 最后拒绝: {{ formatTime(restrictedUser.last_rejected_at) }}
                </span>
              </div>
              <div class="text-xs text-orange-600 font-medium">
                🚫 已限制申请权限
              </div>
            </div>
          </div>

          <div class="flex space-x-2">
            <button
              @click="restoreUserPermission(restrictedUser.user.id)"
              class="px-3 py-1 text-xs bg-green-100 text-green-600 rounded hover:bg-green-200 transition-colors"
              title="恢复该用户的好友申请权限"
            >
              恢复权限
            </button>
            <button
              @click="blockUser(restrictedUser.user.id)"
              class="px-3 py-1 text-xs bg-red-100 text-red-600 rounded hover:bg-red-200 transition-colors"
              title="屏蔽该用户"
            >
              屏蔽
            </button>
          </div>
        </div>
      </div>

      <div v-else class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无被限制用户</h3>
        <p class="mt-1 text-sm text-gray-500">被多次拒绝而限制申请的用户将显示在这里</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { friendsAPI } from '@/services/api'
import { socketService } from '@/services/socket'
import type { User, FriendRequest, BlockedUser } from '@/types'

const emit = defineEmits<{
  startChat: [user: User]
}>()

const props = defineProps<{
  onlineUsers?: { [userId: number]: any }
}>()

const requestType = ref<'received' | 'sent' | 'all'>('received')
const friendRequests = ref<FriendRequest[]>([])
const friends = ref<User[]>([])
const blockedUsers = ref<BlockedUser[]>([])
const restrictedUsers = ref<Array<{
  user: User
  reject_count: number
  last_rejected_at: string | null
  record_id: number
}>>([])

const loadingRequests = ref(false)
const loadingFriends = ref(false)
const loadingBlocked = ref(false)
const loadingRestricted = ref(false)

const requestTypes = [
  { value: 'received', label: '收到的' },
  { value: 'sent', label: '发送的' },
  { value: 'all', label: '全部' }
]

// Socket.IO 事件监听器
const setupSocketListeners = () => {
  console.log('Setting up friend management Socket.IO listeners...')
  console.log('Socket connected:', socketService.isConnected())
  console.log('Socket instance:', socketService.socketInstance)

  // 清理之前的监听器（避免重复监听）
  cleanupSocketListeners()

  // 监听好友被删除事件
  socketService.on('friend_removed', (data: { user_id: number; username: string; removed_by: string }) => {
    console.log('🔥 Friend removed event received in FriendManagement:', data)
    console.log('Current friends before removal:', friends.value.map(f => ({ id: f.id, username: f.username })))

    // 从好友列表中移除该用户
    const beforeCount = friends.value.length
    friends.value = friends.value.filter(friend => friend.id !== data.user_id)
    const afterCount = friends.value.length

    console.log(`🔄 Friend list updated: ${beforeCount} -> ${afterCount} friends`)
    console.log('Current friends after removal:', friends.value.map(f => ({ id: f.id, username: f.username })))

    // 显示通知（可选）
    if (data.removed_by === 'other') {
      console.log(`${data.username} 删除了你的好友关系`)
    } else {
      console.log(`已删除好友 ${data.username}`)
    }
  })

  // 监听好友请求相关事件
  socketService.on('friend_request_received', (data: { request: FriendRequest }) => {
    console.log('Friend request received in friend management:', data)
    // 刷新好友请求列表
    loadFriendRequests()
  })

  socketService.on('friend_request_accepted', (data: { request: FriendRequest }) => {
    console.log('Friend request accepted in friend management:', data)
    // 刷新好友请求列表和好友列表
    loadFriendRequests()
    loadFriends()
  })

  socketService.on('friend_request_declined', (data: { request: FriendRequest }) => {
    console.log('Friend request declined in friend management:', data)
    // 刷新好友请求列表
    loadFriendRequests()
  })
}

// 清理 Socket.IO 事件监听器
const cleanupSocketListeners = () => {
  if (socketService.socketInstance) {
    socketService.off('friend_removed')
    socketService.off('friend_request_received')
    socketService.off('friend_request_accepted')
    socketService.off('friend_request_declined')
  }
}

onMounted(async () => {
  await Promise.all([
    loadFriendRequests(),
    loadFriends(),
    loadBlockedUsers(),
    loadRestrictedUsers()
  ])

  console.log('FriendManagement mounted, friends count:', friends.value.length)

  // 延迟设置 Socket.IO 事件监听，确保 Socket 已连接
  setTimeout(() => {
    console.log('Setting up Socket listeners after delay...')
    setupSocketListeners()
  }, 1000)

  // 监听 Socket 连接事件
  socketService.on('connect', () => {
    console.log('Socket connected, setting up listeners...')
    setupSocketListeners()
  })
})

onUnmounted(() => {
  // 清理 Socket.IO 事件监听器
  cleanupSocketListeners()
})

// 暴露刷新方法给父组件
const refreshData = async () => {
  await Promise.all([
    loadFriendRequests(),
    loadFriends(),
    loadBlockedUsers(),
    loadRestrictedUsers()
  ])
}

// 测试函数 - 模拟接收到 friend_removed 事件
const testFriendRemoved = () => {
  if (friends.value.length > 0) {
    const testFriend = friends.value[0]
    console.log('🧪 Testing friend_removed event with:', testFriend)

    // 模拟接收到 friend_removed 事件
    const testData = {
      user_id: testFriend.id,
      username: testFriend.username,
      removed_by: 'test'
    }

    // 直接调用事件处理逻辑
    console.log('🔥 Friend removed event received in FriendManagement (TEST):', testData)
    console.log('Current friends before removal:', friends.value.map(f => ({ id: f.id, username: f.username })))

    // 从好友列表中移除该用户
    const beforeCount = friends.value.length
    friends.value = friends.value.filter(friend => friend.id !== testData.user_id)
    const afterCount = friends.value.length

    console.log(`🔄 Friend list updated: ${beforeCount} -> ${afterCount} friends`)
    console.log('Current friends after removal:', friends.value.map(f => ({ id: f.id, username: f.username })))
  } else {
    console.log('No friends to test with')
  }
}

// 使用 defineExpose 暴露方法
defineExpose({
  refreshData
})

const loadFriendRequests = async () => {
  loadingRequests.value = true
  try {
    const response = await friendsAPI.getFriendRequests({
      type: requestType.value
    })
    friendRequests.value = response.data.requests
  } catch (error) {
    console.error('Failed to load friend requests:', error)
  } finally {
    loadingRequests.value = false
  }
}

const loadFriends = async () => {
  loadingFriends.value = true
  try {
    const response = await friendsAPI.getFriends({})
    friends.value = response.data.friends
  } catch (error) {
    console.error('Failed to load friends:', error)
  } finally {
    loadingFriends.value = false
  }
}

const loadBlockedUsers = async () => {
  loadingBlocked.value = true
  try {
    const response = await friendsAPI.getBlockedUsers({})
    blockedUsers.value = response.data.blocked_users
  } catch (error) {
    console.error('Failed to load blocked users:', error)
  } finally {
    loadingBlocked.value = false
  }
}

const loadRestrictedUsers = async () => {
  loadingRestricted.value = true
  try {
    const response = await friendsAPI.getRestrictedUsers({})
    restrictedUsers.value = response.data.restricted_users
    console.log('Loaded restricted users:', restrictedUsers.value)
  } catch (error) {
    console.error('Failed to load restricted users:', error)
  } finally {
    loadingRestricted.value = false
  }
}

const acceptRequest = async (requestId: number) => {
  try {
    await friendsAPI.acceptFriendRequest(requestId)
    await Promise.all([loadFriendRequests(), loadFriends()])
  } catch (error) {
    console.error('Failed to accept friend request:', error)
  }
}

const declineRequest = async (requestId: number) => {
  try {
    await friendsAPI.declineFriendRequest(requestId)
    await loadFriendRequests()
  } catch (error) {
    console.error('Failed to decline friend request:', error)
  }
}

const removeFriend = async (friendId: number) => {
  if (!confirm('确定要移除此好友吗？')) return

  console.log('🗑️ Removing friend with ID:', friendId)
  console.log('Current friends before API call:', friends.value.map(f => ({ id: f.id, username: f.username })))

  try {
    await friendsAPI.removeFriend(friendId)
    console.log('✅ Friend removed successfully via API')
    // 不需要立即更新本地列表，等待Socket.IO事件来更新
    // 这样可以确保双方的好友列表都能同步更新
  } catch (error) {
    console.error('❌ Failed to remove friend:', error)
    // 如果删除失败，重新加载列表
    await loadFriends()
  }
}

const unblockUser = async (userId: number) => {
  try {
    await friendsAPI.unblockUser(userId)
    await loadBlockedUsers()
  } catch (error) {
    console.error('Failed to unblock user:', error)
  }
}

const restoreUserPermission = async (userId: number) => {
  if (!confirm('确定要恢复该用户的好友申请权限吗？恢复后该用户可以重新向您发送好友请求。')) return

  try {
    await friendsAPI.restoreUserPermission(userId)
    await loadRestrictedUsers()
    console.log('User permission restored successfully')
  } catch (error) {
    console.error('Failed to restore user permission:', error)
  }
}

const blockUser = async (userId: number) => {
  if (!confirm('确定要屏蔽该用户吗？屏蔽后该用户将无法向您发送消息或好友请求。')) return

  try {
    await friendsAPI.blockUser(userId)
    // 刷新被限制用户列表和屏蔽用户列表
    await Promise.all([loadRestrictedUsers(), loadBlockedUsers()])
    console.log('User blocked successfully')
  } catch (error) {
    console.error('Failed to block user:', error)
  }
}

const startChat = (user: User) => {
  emit('startChat', user)
}

const isOnline = (userId: number) => {
  return props.onlineUsers && props.onlineUsers[userId]
}

const getRequestUsername = (request: FriendRequest) => {
  return requestType.value === 'sent' ? request.receiver_username : request.sender_username
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待处理'
    case 'accepted': return '已接受'
    case 'declined': return '已拒绝'
    default: return status
  }
}

const getEmptyMessage = () => {
  switch (requestType.value) {
    case 'received': return '您没有收到任何好友请求'
    case 'sent': return '您没有发送任何好友请求'
    default: return '暂无好友请求'
  }
}

const formatTime = (timeString: string) => {
  return new Date(timeString).toLocaleString()
}

// 监听请求类型变化
watch(requestType, loadFriendRequests)
</script>
