<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- 顶部导航栏 -->
    <nav class="glass border-b border-white/30 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <RouterLink to="/" class="text-xl font-bold text-gradient">
            1v1 Chat
          </RouterLink>
          <span class="text-sm text-gray-600">Chat页面通知测试</span>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- 通知图标 -->
          <NotificationIcon />
          
          <RouterLink to="/" class="btn-secondary text-sm">
            返回首页
          </RouterLink>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto p-6">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">Chat页面通知功能测试</h1>
        
        <!-- 说明 -->
        <div class="mb-6 p-4 bg-yellow-50 rounded-lg">
          <h2 class="text-lg font-semibold text-yellow-900 mb-2">测试说明</h2>
          <p class="text-yellow-800">
            这个页面模拟Chat页面的环境，用于测试在Chat页面时好友请求通知是否正常工作。
            Chat页面有自己的Socket事件监听器，可能会影响全局的通知监听器。
          </p>
        </div>

        <!-- Socket状态 -->
        <div class="mb-6 p-4 bg-blue-50 rounded-lg">
          <h2 class="text-lg font-semibold text-blue-900 mb-3">Socket连接状态</h2>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-blue-700">连接状态:</span>
              <span :class="socketConnected ? 'text-green-600' : 'text-red-600'" class="ml-2 font-medium">
                {{ socketConnected ? '已连接' : '未连接' }}
              </span>
            </div>
            <div>
              <span class="text-blue-700">Socket ID:</span>
              <span class="ml-2 font-mono text-xs">{{ socketId || '无' }}</span>
            </div>
            <div>
              <span class="text-blue-700">用户认证:</span>
              <span :class="authStore.isAuthenticated ? 'text-green-600' : 'text-red-600'" class="ml-2 font-medium">
                {{ authStore.isAuthenticated ? '已认证' : '未认证' }}
              </span>
            </div>
            <div>
              <span class="text-blue-700">用户名:</span>
              <span class="ml-2 font-medium">{{ authStore.user?.username || '无' }}</span>
            </div>
          </div>
        </div>

        <!-- 测试按钮 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">模拟好友请求</h3>
            <button 
              @click="simulateFriendRequest"
              class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              收到好友请求
            </button>
            <p class="text-xs text-gray-500 mt-2">
              模拟接收到好友请求，测试所有通知功能
            </p>
          </div>

          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">重新设置监听器</h3>
            <button 
              @click="setupGlobalListeners"
              class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              重新设置全局监听器
            </button>
            <p class="text-xs text-gray-500 mt-2">
              手动重新设置全局事件监听器
            </p>
          </div>

          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">Socket重连</h3>
            <button 
              @click="reconnectSocket"
              class="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
            >
              重新连接Socket
            </button>
            <p class="text-xs text-gray-500 mt-2">
              断开并重新连接Socket
            </p>
          </div>

          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">清空通知</h3>
            <button 
              @click="clearNotifications"
              class="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            >
              清空所有通知
            </button>
            <p class="text-xs text-gray-500 mt-2">
              清空所有通知状态
            </p>
          </div>
        </div>

        <!-- 通知状态 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h3 class="font-semibold text-gray-900 mb-3">当前通知状态</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-gray-600">好友请求:</span>
              <span class="font-medium ml-1">{{ notificationsStore.friendRequests.length }}</span>
            </div>
            <div>
              <span class="text-gray-600">未读通知:</span>
              <span class="font-medium ml-1">{{ notificationsStore.unreadCount }}</span>
            </div>
            <div>
              <span class="text-gray-600">总未读:</span>
              <span class="font-medium ml-1">{{ notificationsStore.totalUnreadCount }}</span>
            </div>
            <div>
              <span class="text-gray-600">页面标题:</span>
              <span class="font-medium ml-1">{{ pageTitle }}</span>
            </div>
          </div>
        </div>

        <!-- 事件日志 -->
        <div class="mt-6 bg-gray-50 rounded-lg p-4">
          <div class="flex justify-between items-center mb-3">
            <h3 class="font-semibold text-gray-900">事件日志</h3>
            <button 
              @click="clearLogs"
              class="text-sm text-gray-500 hover:text-gray-700"
            >
              清空日志
            </button>
          </div>
          <div class="h-32 overflow-y-auto bg-white p-2 rounded text-xs font-mono">
            <div v-for="(log, index) in eventLogs" :key="index" class="mb-1">
              <span class="text-gray-500">{{ log.time }}</span>
              <span :class="log.type === 'error' ? 'text-red-600' : log.type === 'success' ? 'text-green-600' : 'text-blue-600'">
                {{ log.message }}
              </span>
            </div>
            <div v-if="eventLogs.length === 0" class="text-gray-400 text-center py-4">
              暂无日志
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useNotificationsStore } from '@/stores/notifications'
import { socketService } from '@/services/socket'
import { notificationService } from '@/services/notificationService'
import { toastService } from '@/services/toastService'
import { audioService } from '@/services/audioService'
import NotificationIcon from '@/components/NotificationIcon.vue'

const authStore = useAuthStore()
const notificationsStore = useNotificationsStore()

const socketConnected = ref(false)
const socketId = ref('')
const pageTitle = ref('')
const eventLogs = ref<Array<{time: string, message: string, type: string}>>([])

const addLog = (message: string, type: string = 'info') => {
  const time = new Date().toLocaleTimeString()
  eventLogs.value.unshift({ time, message, type })
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
  console.log(`[ChatNotificationTest] ${message}`)
}

const updateStatus = () => {
  socketConnected.value = socketService.isConnected()
  socketId.value = socketService.id || ''
  pageTitle.value = document.title
}

const simulateFriendRequest = async () => {
  addLog('开始模拟好友请求...', 'info')
  
  const testRequest = {
    id: Date.now(),
    sender_id: 999,
    sender_username: `Chat测试用户${Math.floor(Math.random() * 100)}`,
    sender_display_name: `Chat测试用户${Math.floor(Math.random() * 100)}`,
    receiver_id: 1,
    receiver_username: '当前用户',
    receiver_display_name: '当前用户',
    status: 'pending' as const,
    message: '这是Chat页面的测试好友请求',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  
  // 添加到store
  notificationsStore.addFriendRequest(testRequest)
  addLog('好友请求已添加到store', 'success')
  
  // 显示各种通知
  try {
    await notificationService.showFriendRequestNotification(testRequest.sender_username)
    addLog('桌面通知已显示', 'success')
  } catch (error) {
    addLog(`桌面通知失败: ${error}`, 'error')
  }
  
  try {
    toastService.showFriendRequestToast(testRequest.sender_username)
    addLog('Toast通知已显示', 'success')
  } catch (error) {
    addLog(`Toast通知失败: ${error}`, 'error')
  }
  
  try {
    await audioService.playNotificationSound()
    addLog('音效已播放', 'success')
  } catch (error) {
    addLog(`音效播放失败: ${error}`, 'error')
  }
}

const setupGlobalListeners = () => {
  addLog('重新设置全局事件监听器...', 'info')
  
  // 这里调用App.vue中的setupSocketListeners逻辑
  socketService.off('friend_request_received')
  socketService.off('friend_request_accepted')
  socketService.off('friend_request_declined')
  
  socketService.on('friend_request_received', (data) => {
    addLog(`收到好友请求事件: ${data.request?.sender_username}`, 'success')
  })
  
  socketService.on('friend_request_accepted', (data) => {
    addLog(`好友请求被接受: ${data.request?.receiver_username}`, 'success')
  })
  
  socketService.on('friend_request_declined', (data) => {
    addLog(`好友请求被拒绝: ${data.request?.receiver_username}`, 'error')
  })
  
  addLog('全局监听器设置完成', 'success')
}

const reconnectSocket = async () => {
  addLog('⚠️ 测试功能：开始重新连接Socket...', 'info')
  addLog('⚠️ 注意：这会断开全局Socket连接，可能影响其他页面的通知功能', 'warning')

  socketService.disconnect()
  addLog('Socket已断开', 'info')

  setTimeout(async () => {
    try {
      await socketService.connect()
      addLog('Socket重新连接成功', 'success')
      updateStatus()

      // 重新设置监听器
      setTimeout(() => {
        setupGlobalListeners()
      }, 100)
    } catch (error) {
      addLog(`Socket重连失败: ${error}`, 'error')
    }
  }, 1000)
}

const clearNotifications = () => {
  notificationsStore.clearAllNotifications()
  notificationsStore.clearFriendRequests()
  addLog('所有通知已清空', 'info')
}

const clearLogs = () => {
  eventLogs.value = []
}

onMounted(() => {
  addLog('Chat通知测试页面已加载', 'info')
  updateStatus()
  
  // 设置状态更新定时器
  const statusInterval = setInterval(updateStatus, 1000)
  
  onUnmounted(() => {
    clearInterval(statusInterval)
  })
})
</script>

<style scoped>
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.btn-secondary {
  @apply px-4 py-2 bg-white/80 text-gray-700 rounded-lg hover:bg-white/90 transition-colors border border-gray-200;
}
</style>
