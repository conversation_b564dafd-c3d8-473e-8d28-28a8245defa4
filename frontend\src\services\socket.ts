import { io, type Socket } from 'socket.io-client'
import type { ExtendedSocketEvents } from '@/types'

class SocketService {
  private socket: Socket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private isConnecting = false
  private eventListenersSetup = false
  private customListenersSetup = false

  constructor() {
    // 不再需要从cookie获取token，因为JWT cookie是HttpOnly的
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        console.log('Socket already connected:', this.id)
        resolve()
        return
      }

      if (this.isConnecting) {
        console.log('Socket connection already in progress, waiting...')
        // 等待当前连接完成
        const checkConnection = () => {
          if (this.socket?.connected) {
            resolve()
          } else if (!this.isConnecting) {
            reject(new Error('Connection failed'))
          } else {
            setTimeout(checkConnection, 100)
          }
        }
        checkConnection()
        return
      }

      // 如果有旧的socket实例，先清理
      if (this.socket) {
        console.log('Cleaning up old socket instance')
        this.cleanupSocket()
      }

      this.isConnecting = true
      const serverUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:5000'
      console.log('Attempting to connect to Socket.IO server:', serverUrl)

      // 使用withCredentials方式连接，让Socket.IO自动发送HttpOnly cookies
      this.socket = io(serverUrl, {
        withCredentials: true, // 自动发送cookies，包括HttpOnly的JWT cookie
        transports: ['websocket', 'polling'],
        timeout: 10000,
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
      })

      this.setupBasicEventListeners()

      this.socket.on('connect', () => {
        console.log('Socket connected successfully:', this.id)
        this.reconnectAttempts = 0
        this.isConnecting = false

        // 不在这里立即请求在线用户列表，让调用方决定何时请求
        // 这样可以避免时序问题

        resolve()
      })

      this.socket.on('connect_error', (error) => {
        console.error('Socket connection error:', error)
        console.error('Error details:', {
          message: error.message,
          description: error.description,
          context: error.context,
          type: error.type
        })
        this.isConnecting = false
        reject(error)
      })
    })
  }

  private setupBasicEventListeners() {
    if (!this.socket || this.eventListenersSetup) return

    this.socket.on('connected', (data) => {
      console.log('Received connected event:', data)
    })

    this.socket.on('error', (error) => {
      console.error('Socket error:', error)
    })

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason)
      this.isConnecting = false

      // 自动重连逻辑
      if (reason === 'io server disconnect') {
        // 服务器主动断开，不自动重连
        console.log('Server disconnected the socket, not attempting to reconnect')
      } else {
        // 其他原因断开，尝试重连
        this.handleReconnect()
      }
    })

    this.eventListenersSetup = true
  }

  private cleanupSocket() {
    if (this.socket) {
      console.log('Cleaning up socket and removing all listeners')
      this.socket.removeAllListeners()
      this.socket.disconnect()
      this.socket = null
      this.eventListenersSetup = false
      this.customListenersSetup = false
    }
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect().catch(console.error)
      }, this.reconnectDelay * this.reconnectAttempts)
    } else {
      console.error('Max reconnection attempts reached')
    }
  }

  disconnect() {
    console.log('Disconnecting socket...')
    this.isConnecting = false
    this.cleanupSocket()
  }

  // 检查连接状态
  isConnected() {
    return this.socket?.connected || false
  }

  // 发送消息
  sendMessage(data: {
    content: string;
    recipient_id: number;
    reply_to?: number;
    message_type?: 'text' | 'image' | 'questionnaire' | 'questionnaire_response';
    image_url?: string;
    image_thumbnail_url?: string;
    questionnaire_id?: number;
    questionnaire_response_id?: number;
  }) {
    this.socket?.emit('send_message', data)
  }

  // 撤回消息
  recallMessage(messageId: number) {
    this.socket?.emit('recall_message', { message_id: messageId })
  }

  // 发送输入状态
  sendTyping(recipientId: number, isTyping: boolean) {
    this.socket?.emit('typing', { recipient_id: recipientId, is_typing: isTyping })
  }

  // 加入对话
  joinConversation(userId: number) {
    this.socket?.emit('join_conversation', { user_id: userId })
  }

  // 离开对话
  leaveConversation(userId: number) {
    this.socket?.emit('leave_conversation', { user_id: userId })
  }

  // 获取在线用户
  getOnlineUsers() {
    if (!this.socket?.connected) {
      console.warn('Cannot get online users: socket not connected')
      return
    }
    console.log('Requesting online users from server')
    this.socket.emit('get_online_users')
  }

  // 监听事件
  on<K extends keyof ExtendedSocketEvents>(event: K, callback: ExtendedSocketEvents[K]) {
    if (!this.socket) {
      console.warn(`Cannot add listener for ${event}: socket not initialized`)
      return
    }
    this.socket.on(event, callback)
    // 标记自定义监听器已设置（排除基础事件）
    if (!['connect', 'disconnect', 'connect_error', 'error'].includes(event as string)) {
      this.customListenersSetup = true
    }
  }

  // 移除事件监听
  off<K extends keyof ExtendedSocketEvents>(event: K, callback?: ExtendedSocketEvents[K]) {
    if (!this.socket) {
      console.warn(`Cannot remove listener for ${event}: socket not initialized`)
      return
    }
    this.socket.off(event, callback)
  }

  // 监听一次性事件
  once<K extends keyof ExtendedSocketEvents>(event: K, callback: ExtendedSocketEvents[K]) {
    if (!this.socket) {
      console.warn(`Cannot add once listener for ${event}: socket not initialized`)
      return
    }
    this.socket.once(event, callback)
  }

  // 移除所有事件监听器
  removeAllListeners() {
    if (this.socket) {
      this.socket.removeAllListeners()
      this.customListenersSetup = false
    }
  }

  // 检查是否已设置自定义监听器
  hasListeners(): boolean {
    return this.customListenersSetup
  }

  // 获取连接状态
  get connected(): boolean {
    return this.socket?.connected ?? false
  }

  // 获取socket ID
  get id(): string | undefined {
    return this.socket?.id
  }

  // 获取socket实例（用于检查是否已初始化）
  get socketInstance(): Socket | null {
    return this.socket
  }

  // 重新连接（当认证状态改变时）
  reconnectWithNewAuth() {
    if (this.socket?.connected) {
      // 如果已连接，需要重新连接以更新认证
      this.disconnect()
      this.connect().catch(console.error)
    }
  }
}

// 创建单例实例
export const socketService = new SocketService()

// 导出类型以供其他地方使用
export type { ExtendedSocketEvents }
export default socketService
