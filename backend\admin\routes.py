import csv
import io
from datetime import datetime, timedelta
from flask import request, jsonify, make_response
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import func, desc, case

from . import admin_bp
from models import db, User, Message, ChatLink, InviteCode, get_local_time

def admin_required(f):
    """管理员权限装饰器"""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_id = get_jwt_identity()
        print(f"Admin check - user_id: {user_id}")  # 调试信息

        if not user_id:
            print("No user_id found in JWT token")  # 调试信息
            return jsonify({'error': 'Admin access required'}), 403

        user = User.query.get(user_id)
        print(f"Admin check - user: {user}, is_admin: {user.is_admin if user else None}")  # 调试信息

        if not user or not user.is_admin:
            return jsonify({'error': 'Admin access required'}), 403

        return f(*args, **kwargs)

    return decorated_function

@admin_bp.route('/stats', methods=['GET'])
@jwt_required()
@admin_required
def get_stats():
    """获取统计信息"""
    try:
        # 用户统计
        total_users = User.query.count()
        anonymous_users = User.query.filter_by(is_anonymous=True).count()
        registered_users = User.query.filter_by(is_anonymous=False).count()
        banned_users = User.query.filter_by(banned=True).count()
        
        # 24小时内活跃用户
        yesterday = datetime.now() - timedelta(hours=24)
        active_users_24h = User.query.filter(User.last_seen >= yesterday).count()

        # 消息统计
        total_messages = Message.query.count()
        messages_24h = Message.query.filter(Message.timestamp >= yesterday).count()
        recalled_messages = Message.query.filter_by(is_recalled=True).count()
        
        # 链接统计
        total_links = ChatLink.query.count()
        used_links = ChatLink.query.filter_by(is_used=True).count()
        active_links = ChatLink.query.filter(
            ChatLink.expires_at > datetime.utcnow(),
            ChatLink.is_used == False
        ).count()
        
        # 邀请码统计
        total_invites = InviteCode.query.count()
        used_invites = InviteCode.query.filter_by(is_used=True).count()
        
        return jsonify({
            'users': {
                'total': total_users,
                'anonymous': anonymous_users,
                'registered': registered_users,
                'banned': banned_users,
                'active_24h': active_users_24h
            },
            'messages': {
                'total': total_messages,
                'last_24h': messages_24h,
                'recalled': recalled_messages
            },
            'links': {
                'total': total_links,
                'used': used_links,
                'active': active_links
            },
            'invites': {
                'total': total_invites,
                'used': used_invites,
                'available': total_invites - used_invites
            }
        })
        
    except Exception as e:
        return jsonify({'error': 'Failed to get statistics'}), 500

@admin_bp.route('/users', methods=['GET'])
@jwt_required()
@admin_required
def get_users():
    """获取用户列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '').strip()
    user_type = request.args.get('type', 'all')  # all, anonymous, registered, banned
    
    query = User.query
    
    # 搜索过滤
    if search:
        query = query.filter(User.username.contains(search))
    
    # 类型过滤
    if user_type == 'anonymous':
        query = query.filter_by(is_anonymous=True)
    elif user_type == 'registered':
        query = query.filter_by(is_anonymous=False)
    elif user_type == 'banned':
        query = query.filter_by(banned=True)
    
    # 分页
    users = query.order_by(desc(User.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'users': [user.to_dict() for user in users.items],
        'pagination': {
            'page': page,
            'pages': users.pages,
            'per_page': per_page,
            'total': users.total,
            'has_next': users.has_next,
            'has_prev': users.has_prev
        }
    })

@admin_bp.route('/users/<int:user_id>/ban', methods=['PUT'])
@jwt_required()
@admin_required
def ban_user(user_id):
    """封禁用户"""
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    if user.is_admin:
        return jsonify({'error': 'Cannot ban admin user'}), 400
    
    try:
        user.banned = True
        db.session.commit()
        
        return jsonify({
            'message': f'User {user.username} has been banned',
            'user': user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to ban user'}), 500

@admin_bp.route('/users/<int:user_id>/unban', methods=['PUT'])
@jwt_required()
@admin_required
def unban_user(user_id):
    """解封用户"""
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    try:
        user.banned = False
        db.session.commit()
        
        return jsonify({
            'message': f'User {user.username} has been unbanned',
            'user': user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to unban user'}), 500

@admin_bp.route('/users/<int:user_id>/mute', methods=['PUT'])
@jwt_required()
@admin_required
def mute_user(user_id):
    """禁言用户"""
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    if user.is_admin:
        return jsonify({'error': 'Cannot mute admin user'}), 400
    
    try:
        user.muted = True
        db.session.commit()
        
        return jsonify({
            'message': f'User {user.username} has been muted',
            'user': user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to mute user'}), 500

@admin_bp.route('/users/<int:user_id>/unmute', methods=['PUT'])
@jwt_required()
@admin_required
def unmute_user(user_id):
    """解除禁言"""
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    try:
        user.muted = False
        db.session.commit()
        
        return jsonify({
            'message': f'User {user.username} has been unmuted',
            'user': user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to unmute user'}), 500

@admin_bp.route('/invites/generate', methods=['POST'])
@jwt_required()
@admin_required
def generate_invites():
    """生成邀请码"""
    data = request.get_json() or {}
    count = data.get('count', 10)
    note = data.get('note', '').strip()
    creator_id = get_jwt_identity()  # 管理员作为创建者

    if count < 1 or count > 100:
        return jsonify({'error': 'Count must be between 1 and 100'}), 400

    # 备注长度限制
    if note and len(note) > 255:
        return jsonify({'error': 'Note must be less than 255 characters'}), 400

    try:
        invites = InviteCode.create_batch(count, creator_id=creator_id, note=note if note else None)

        for invite in invites:
            db.session.add(invite)

        db.session.commit()

        return jsonify({
            'message': f'Generated {count} invite codes',
            'invites': [invite.to_dict() for invite in invites]
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to generate invite codes'}), 500

@admin_bp.route('/invites', methods=['GET'])
@jwt_required()
@admin_required
def get_invites():
    """获取邀请码列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    status = request.args.get('status', 'all')  # all, used, unused
    
    query = InviteCode.query
    
    if status == 'used':
        query = query.filter_by(is_used=True)
    elif status == 'unused':
        query = query.filter_by(is_used=False)
    
    invites = query.order_by(desc(InviteCode.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'invites': [invite.to_dict() for invite in invites.items],
        'pagination': {
            'page': page,
            'pages': invites.pages,
            'per_page': per_page,
            'total': invites.total,
            'has_next': invites.has_next,
            'has_prev': invites.has_prev
        }
    })

@admin_bp.route('/invites/export', methods=['GET'])
@jwt_required()
@admin_required
def export_invites():
    """导出邀请码为CSV"""
    status = request.args.get('status', 'unused')
    
    query = InviteCode.query
    if status == 'used':
        query = query.filter_by(is_used=True)
    elif status == 'unused':
        query = query.filter_by(is_used=False)
    
    invites = query.order_by(desc(InviteCode.created_at)).all()
    
    # 创建CSV
    output = io.StringIO()
    writer = csv.writer(output)
    
    # 写入标题行
    writer.writerow(['Code', 'Status', 'Created At', 'Used At', 'Used By'])
    
    # 写入数据
    for invite in invites:
        writer.writerow([
            invite.code,
            'Used' if invite.is_used else 'Unused',
            invite.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            invite.used_at.strftime('%Y-%m-%d %H:%M:%S') if invite.used_at else '',
            invite.used_by if invite.used_by else ''
        ])
    
    # 创建响应
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=invite_codes_{status}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
    
    return response

@admin_bp.route('/messages', methods=['GET'])
@jwt_required()
@admin_required
def get_messages():
    """获取消息列表（管理员查看）"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    user_id = request.args.get('user_id', type=int)
    sender_id = request.args.get('sender_id', type=int)
    recipient_id = request.args.get('recipient_id', type=int)
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    search = request.args.get('search', '').strip()

    query = Message.query

    # 用户过滤（发送者或接收者）
    if user_id:
        query = query.filter(
            (Message.sender_id == user_id) | (Message.recipient_id == user_id)
        )

    # 发送者过滤
    if sender_id:
        query = query.filter(Message.sender_id == sender_id)

    # 接收者过滤
    if recipient_id:
        query = query.filter(Message.recipient_id == recipient_id)

    # 时间范围过滤
    if start_date:
        try:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            query = query.filter(Message.timestamp >= start_dt)
        except ValueError:
            pass

    if end_date:
        try:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            query = query.filter(Message.timestamp <= end_dt)
        except ValueError:
            pass

    # 内容搜索
    if search:
        query = query.filter(Message.content.contains(search))

    messages = query.order_by(desc(Message.timestamp)).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # 获取消息详情，包括发送者和接收者信息
    message_list = []
    for msg in messages.items:
        msg_dict = msg.to_dict()
        # 添加发送者和接收者信息
        sender = User.query.get(msg.sender_id)
        recipient = User.query.get(msg.recipient_id)
        msg_dict['sender'] = sender.to_dict() if sender else None
        msg_dict['recipient'] = recipient.to_dict() if recipient else None
        message_list.append(msg_dict)

    return jsonify({
        'messages': message_list,
        'pagination': {
            'page': page,
            'pages': messages.pages,
            'per_page': per_page,
            'total': messages.total,
            'has_next': messages.has_next,
            'has_prev': messages.has_prev
        }
    })

@admin_bp.route('/users/<int:user_id>', methods=['GET'])
@jwt_required()
@admin_required
def get_user_detail(user_id):
    """获取用户详情"""
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404

    try:
        # 获取用户统计信息
        sent_messages = Message.query.filter_by(sender_id=user_id).count()
        received_messages = Message.query.filter_by(recipient_id=user_id).count()
        total_messages = sent_messages + received_messages

        # 获取最近7天的活动
        week_ago = get_local_time() - timedelta(days=7)
        recent_messages = Message.query.filter(
            (Message.sender_id == user_id) | (Message.recipient_id == user_id),
            Message.timestamp >= week_ago
        ).count()

        # 获取聊天对象数量
        chat_partners = db.session.query(
            func.count(func.distinct(
                func.case(
                    (Message.sender_id == user_id, Message.recipient_id),
                    else_=Message.sender_id
                )
            ))
        ).filter(
            (Message.sender_id == user_id) | (Message.recipient_id == user_id)
        ).scalar() or 0

        # 获取最近的消息
        recent_messages_list = Message.query.filter(
            (Message.sender_id == user_id) | (Message.recipient_id == user_id)
        ).order_by(desc(Message.timestamp)).limit(10).all()

        recent_messages_data = []
        for msg in recent_messages_list:
            msg_dict = msg.to_dict()
            if msg.sender_id != user_id:
                sender = User.query.get(msg.sender_id)
                msg_dict['other_user'] = sender.to_dict() if sender else None
                msg_dict['direction'] = 'received'
            else:
                recipient = User.query.get(msg.recipient_id)
                msg_dict['other_user'] = recipient.to_dict() if recipient else None
                msg_dict['direction'] = 'sent'
            recent_messages_data.append(msg_dict)

        user_detail = user.to_dict()
        user_detail['statistics'] = {
            'sent_messages': sent_messages,
            'received_messages': received_messages,
            'total_messages': total_messages,
            'recent_messages_7d': recent_messages,
            'chat_partners': chat_partners
        }
        user_detail['recent_messages'] = recent_messages_data

        return jsonify({'user': user_detail})

    except Exception as e:
        return jsonify({'error': 'Failed to get user details'}), 500

@admin_bp.route('/messages/export', methods=['GET'])
@jwt_required()
@admin_required
def export_messages():
    """导出聊天记录"""
    format_type = request.args.get('format', 'csv')  # csv 或 json
    user_id = request.args.get('user_id', type=int)
    sender_id = request.args.get('sender_id', type=int)
    recipient_id = request.args.get('recipient_id', type=int)
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    limit = request.args.get('limit', 10000, type=int)  # 限制导出数量

    try:
        query = Message.query

        # 应用过滤条件
        if user_id:
            query = query.filter(
                (Message.sender_id == user_id) | (Message.recipient_id == user_id)
            )

        if sender_id:
            query = query.filter(Message.sender_id == sender_id)

        if recipient_id:
            query = query.filter(Message.recipient_id == recipient_id)

        if start_date:
            try:
                start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                query = query.filter(Message.timestamp >= start_dt)
            except ValueError:
                pass

        if end_date:
            try:
                end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                query = query.filter(Message.timestamp <= end_dt)
            except ValueError:
                pass

        # 获取消息数据
        messages = query.order_by(desc(Message.timestamp)).limit(limit).all()

        if format_type == 'json':
            # JSON格式导出
            export_data = []
            for msg in messages:
                msg_dict = msg.to_dict()
                sender = User.query.get(msg.sender_id)
                recipient = User.query.get(msg.recipient_id)
                msg_dict['sender'] = sender.to_dict() if sender else None
                msg_dict['recipient'] = recipient.to_dict() if recipient else None
                export_data.append(msg_dict)

            response = make_response(jsonify({
                'messages': export_data,
                'export_info': {
                    'total_count': len(export_data),
                    'export_time': datetime.now().isoformat(),
                    'filters': {
                        'user_id': user_id,
                        'sender_id': sender_id,
                        'recipient_id': recipient_id,
                        'start_date': start_date,
                        'end_date': end_date
                    }
                }
            }))
            response.headers['Content-Type'] = 'application/json'
            response.headers['Content-Disposition'] = f'attachment; filename=chat_messages_{get_local_time().strftime("%Y%m%d_%H%M%S")}.json'

        else:
            # CSV格式导出
            output = io.StringIO()
            writer = csv.writer(output)

            # 写入表头
            writer.writerow([
                'ID', '发送时间', '发送者ID', '发送者用户名', '接收者ID', '接收者用户名',
                '消息内容', '是否撤回', '回复消息ID'
            ])

            # 写入数据
            for msg in messages:
                sender = User.query.get(msg.sender_id)
                recipient = User.query.get(msg.recipient_id)
                writer.writerow([
                    msg.id,
                    msg.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    msg.sender_id,
                    sender.username if sender else 'Unknown',
                    msg.recipient_id,
                    recipient.username if recipient else 'Unknown',
                    msg.content,
                    '是' if msg.is_recalled else '否',
                    msg.reply_to or ''
                ])

            output.seek(0)
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = f'attachment; filename=chat_messages_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

        return response

    except Exception as e:
        return jsonify({'error': 'Failed to export messages'}), 500

@admin_bp.route('/users/batch', methods=['POST'])
@jwt_required()
@admin_required
def batch_user_operations():
    """批量用户操作"""
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    user_ids = data.get('user_ids', [])
    operation = data.get('operation')  # ban, unban, mute, unmute, delete

    if not user_ids or not operation:
        return jsonify({'error': 'user_ids and operation are required'}), 400

    if operation not in ['ban', 'unban', 'mute', 'unmute', 'delete']:
        return jsonify({'error': 'Invalid operation'}), 400

    try:
        affected_users = []
        admin_user_id = get_jwt_identity()

        for user_id in user_ids:
            user = User.query.get(user_id)
            if not user:
                continue

            # 不能对管理员执行操作
            if user.is_admin:
                continue

            # 不能对自己执行操作
            if user.id == admin_user_id:
                continue

            if operation == 'ban':
                user.banned = True
            elif operation == 'unban':
                user.banned = False
            elif operation == 'mute':
                user.muted = True
            elif operation == 'unmute':
                user.muted = False
            elif operation == 'delete':
                # 删除用户的所有消息
                Message.query.filter(
                    (Message.sender_id == user.id) | (Message.recipient_id == user.id)
                ).delete()
                # 删除用户
                db.session.delete(user)

            if operation != 'delete':
                affected_users.append(user.to_dict())

        db.session.commit()

        return jsonify({
            'message': f'Successfully {operation}ed {len(affected_users)} users',
            'affected_users': affected_users
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Failed to perform batch operation: {str(e)}'}), 500

@admin_bp.route('/users/<int:user_id>/activity', methods=['GET'])
@jwt_required()
@admin_required
def get_user_activity(user_id):
    """获取用户活动统计"""
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404

    try:
        days = request.args.get('days', 30, type=int)
        start_date = get_local_time() - timedelta(days=days)

        # 按天统计消息数量
        daily_stats = db.session.query(
            func.date(Message.timestamp).label('date'),
            func.count(Message.id).label('message_count')
        ).filter(
            (Message.sender_id == user_id) | (Message.recipient_id == user_id),
            Message.timestamp >= start_date
        ).group_by(func.date(Message.timestamp)).all()

        # 聊天对象统计
        chat_partners = db.session.query(
            func.case(
                (Message.sender_id == user_id, Message.recipient_id),
                else_=Message.sender_id
            ).label('partner_id'),
            func.count(Message.id).label('message_count')
        ).filter(
            (Message.sender_id == user_id) | (Message.recipient_id == user_id),
            Message.timestamp >= start_date
        ).group_by('partner_id').all()

        # 获取聊天对象详情
        partner_stats = []
        for partner_id, msg_count in chat_partners:
            partner = User.query.get(partner_id)
            if partner:
                partner_stats.append({
                    'user': partner.to_dict(),
                    'message_count': msg_count
                })

        # 按小时统计活动时间
        hourly_stats = db.session.query(
            func.extract('hour', Message.timestamp).label('hour'),
            func.count(Message.id).label('message_count')
        ).filter(
            (Message.sender_id == user_id) | (Message.recipient_id == user_id),
            Message.timestamp >= start_date
        ).group_by('hour').all()

        return jsonify({
            'user_id': user_id,
            'period_days': days,
            'daily_activity': [
                {
                    'date': stat.date.isoformat(),
                    'message_count': stat.message_count
                } for stat in daily_stats
            ],
            'chat_partners': partner_stats,
            'hourly_activity': [
                {
                    'hour': int(stat.hour),
                    'message_count': stat.message_count
                } for stat in hourly_stats
            ]
        })

    except Exception as e:
        return jsonify({'error': 'Failed to get user activity'}), 500

@admin_bp.route('/users/<int:user_id>/group-permission', methods=['PUT'])
@jwt_required()
@admin_required
def update_group_permission(user_id):
    """更新用户群组创建权限"""
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    can_create_groups = data.get('can_create_groups')
    if can_create_groups is None:
        return jsonify({'error': 'can_create_groups field is required'}), 400

    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404

    try:
        user.can_create_groups = bool(can_create_groups)
        db.session.commit()

        return jsonify({
            'message': f'Group creation permission {"granted" if can_create_groups else "revoked"} for user {user.username}',
            'user': user.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to update group permission'}), 500

@admin_bp.route('/groups', methods=['GET'])
@jwt_required()
@admin_required
def get_all_groups():
    """获取所有群组列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '').strip()

    from models import Group, GroupMembership

    query = Group.query

    # 搜索过滤
    if search:
        query = query.filter(Group.name.contains(search))

    groups = query.order_by(desc(Group.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )

    group_list = []
    for group in groups.items:
        group_dict = group.to_dict()
        # 添加额外的统计信息
        group_dict['admin_count'] = GroupMembership.query.filter_by(
            group_id=group.id, role='admin'
        ).count()
        group_list.append(group_dict)

    return jsonify({
        'groups': group_list,
        'pagination': {
            'page': page,
            'pages': groups.pages,
            'per_page': per_page,
            'total': groups.total,
            'has_next': groups.has_next,
            'has_prev': groups.has_prev
        }
    })

@admin_bp.route('/groups/<int:group_id>', methods=['GET'])
@jwt_required()
@admin_required
def get_group_admin_details(group_id):
    """获取群组详细信息（管理员视图）"""
    from models import Group, GroupMembership, GroupMessage

    group = Group.query.get(group_id)
    if not group:
        return jsonify({'error': 'Group not found'}), 404

    try:
        # 获取群组基本信息
        group_dict = group.to_dict()

        # 获取成员列表
        members = []
        for membership in group.memberships:
            member_dict = membership.to_dict()
            members.append(member_dict)

        # 获取消息统计
        total_messages = GroupMessage.query.filter_by(group_id=group_id).count()
        recent_messages = GroupMessage.query.filter_by(group_id=group_id).filter(
            GroupMessage.timestamp >= datetime.utcnow() - timedelta(days=7)
        ).count()

        group_dict['members'] = members
        group_dict['statistics'] = {
            'total_messages': total_messages,
            'recent_messages_7d': recent_messages
        }

        return jsonify({'group': group_dict})

    except Exception as e:
        return jsonify({'error': 'Failed to get group details'}), 500

@admin_bp.route('/groups/<int:group_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def admin_delete_group(group_id):
    """管理员删除群组"""
    from models import Group

    group = Group.query.get(group_id)
    if not group:
        return jsonify({'error': 'Group not found'}), 404

    try:
        group_name = group.name
        db.session.delete(group)
        db.session.commit()

        return jsonify({
            'message': f'Group "{group_name}" has been deleted by admin'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to delete group'}), 500

@admin_bp.route('/friend-requests', methods=['GET'])
@jwt_required()
@admin_required
def get_all_friend_requests():
    """获取所有好友请求（管理员视图）"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    status = request.args.get('status', 'all')  # all, pending, accepted, declined

    from models import FriendRequest

    query = FriendRequest.query

    if status != 'all':
        query = query.filter_by(status=status)

    requests = query.order_by(desc(FriendRequest.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return jsonify({
        'friend_requests': [req.to_dict() for req in requests.items],
        'pagination': {
            'page': page,
            'pages': requests.pages,
            'per_page': per_page,
            'total': requests.total,
            'has_next': requests.has_next,
            'has_prev': requests.has_prev
        }
    })

@admin_bp.route('/blocked-users', methods=['GET'])
@jwt_required()
@admin_required
def get_all_blocked_relationships():
    """获取所有屏蔽关系（管理员视图）"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    from models import BlockedUser

    blocked_relationships = BlockedUser.query.order_by(
        desc(BlockedUser.created_at)
    ).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return jsonify({
        'blocked_relationships': [rel.to_dict() for rel in blocked_relationships.items],
        'pagination': {
            'page': page,
            'pages': blocked_relationships.pages,
            'per_page': per_page,
            'total': blocked_relationships.total,
            'has_next': blocked_relationships.has_next,
            'has_prev': blocked_relationships.has_prev
        }
    })

@admin_bp.route('/conversations', methods=['GET'])
@jwt_required()
@admin_required
def get_all_conversations():
    """获取所有会话列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    try:
        # 获取所有会话的最新消息
        # 使用CASE语句替代least/greatest函数（SQLite兼容）
        latest_messages = db.session.query(
            case(
                (Message.sender_id < Message.recipient_id, Message.sender_id),
                else_=Message.recipient_id
            ).label('user1'),
            case(
                (Message.sender_id > Message.recipient_id, Message.sender_id),
                else_=Message.recipient_id
            ).label('user2'),
            func.max(Message.id).label('latest_message_id'),
            func.count(Message.id).label('message_count')
        ).group_by('user1', 'user2').subquery()

        # 获取会话详情
        conversations_query = db.session.query(
            latest_messages.c.user1,
            latest_messages.c.user2,
            latest_messages.c.message_count,
            Message
        ).join(
            Message, Message.id == latest_messages.c.latest_message_id
        ).order_by(desc(Message.timestamp))

        # 分页
        total = conversations_query.count()
        conversations = conversations_query.offset((page - 1) * per_page).limit(per_page).all()

        conversation_list = []
        for conv in conversations:
            user1 = User.query.get(conv.user1)
            user2 = User.query.get(conv.user2)
            latest_msg = conv.Message

            conversation_list.append({
                'participants': [
                    user1.to_dict() if user1 else None,
                    user2.to_dict() if user2 else None
                ],
                'message_count': conv.message_count,
                'latest_message': latest_msg.to_dict(),
                'last_activity': latest_msg.timestamp.isoformat()
            })

        return jsonify({
            'conversations': conversation_list,
            'pagination': {
                'page': page,
                'pages': (total + per_page - 1) // per_page,
                'per_page': per_page,
                'total': total,
                'has_next': page * per_page < total,
                'has_prev': page > 1
            }
        })

    except Exception as e:
        return jsonify({'error': 'Failed to get conversations'}), 500

@admin_bp.route('/links', methods=['GET'])
@jwt_required()
@admin_required
def get_chat_links():
    """获取聊天链接列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    
    links = ChatLink.query.order_by(desc(ChatLink.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'links': [link.to_dict() for link in links.items],
        'pagination': {
            'page': page,
            'pages': links.pages,
            'per_page': per_page,
            'total': links.total,
            'has_next': links.has_next,
            'has_prev': links.has_prev
        }
    })
