<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- 顶部导航栏 -->
    <nav class="glass border-b border-white/30 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <RouterLink to="/" class="text-xl font-bold text-gradient">
            1v1 Chat
          </RouterLink>
          <span class="text-sm text-gray-600">多用户通知测试</span>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- 通知图标 -->
          <NotificationIcon />
          
          <RouterLink to="/" class="btn-secondary text-sm">
            返回首页
          </RouterLink>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-6xl mx-auto p-6">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">多用户好友请求通知测试</h1>
        
        <!-- 说明 -->
        <div class="mb-6 p-4 bg-yellow-50 rounded-lg">
          <h2 class="text-lg font-semibold text-yellow-900 mb-2">测试说明</h2>
          <ul class="text-yellow-800 space-y-1">
            <li>• 这个页面用于测试多个用户之间的好友请求通知</li>
            <li>• 请在两个不同的浏览器窗口中登录不同的用户</li>
            <li>• 检查Socket连接状态和事件监听器</li>
            <li>• 测试好友请求通知是否能正确发送和接收</li>
          </ul>
        </div>

        <!-- 当前用户信息 -->
        <div class="mb-6 p-4 bg-blue-50 rounded-lg">
          <h2 class="text-lg font-semibold text-blue-900 mb-3">当前用户信息</h2>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-blue-700">用户名:</span>
              <span class="ml-2 font-medium">{{ authStore.user?.username || '未登录' }}</span>
            </div>
            <div>
              <span class="text-blue-700">用户ID:</span>
              <span class="ml-2 font-medium">{{ authStore.user?.id || '无' }}</span>
            </div>
            <div>
              <span class="text-blue-700">Socket连接:</span>
              <span :class="socketConnected ? 'text-green-600' : 'text-red-600'" class="ml-2 font-medium">
                {{ socketConnected ? '已连接' : '未连接' }}
              </span>
            </div>
            <div>
              <span class="text-blue-700">Socket ID:</span>
              <span class="ml-2 font-mono text-xs">{{ socketId || '无' }}</span>
            </div>
          </div>
        </div>

        <!-- 测试工具 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- Socket连接测试 -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">Socket连接测试</h3>
            <div class="space-y-2">
              <button 
                @click="testSocketConnection"
                class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                测试Socket连接
              </button>
              <button 
                @click="reconnectSocket"
                class="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
              >
                重新连接Socket
              </button>
              <button 
                @click="setupEventListeners"
                class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
              >
                重新设置事件监听器
              </button>
            </div>
          </div>

          <!-- 好友请求测试 -->
          <div class="p-4 border border-gray-200 rounded-lg">
            <h3 class="font-semibold text-gray-900 mb-3">好友请求测试</h3>
            <div class="space-y-2">
              <input 
                v-model="targetUsername"
                placeholder="输入目标用户名"
                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
              <button 
                @click="sendFriendRequest"
                class="w-full px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
                :disabled="!targetUsername || sending"
              >
                {{ sending ? '发送中...' : '发送好友请求' }}
              </button>
              <button 
                @click="searchUsers"
                class="w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
              >
                搜索用户
              </button>
            </div>
          </div>
        </div>

        <!-- 搜索结果 -->
        <div v-if="searchResults.length > 0" class="mb-6 p-4 bg-green-50 rounded-lg">
          <h3 class="font-semibold text-green-900 mb-3">搜索结果</h3>
          <div class="space-y-2">
            <div 
              v-for="user in searchResults" 
              :key="user.id"
              class="flex justify-between items-center bg-white p-3 rounded border"
            >
              <div>
                <span class="font-medium">{{ user.username }}</span>
                <span class="text-gray-500 ml-2">(ID: {{ user.id }})</span>
              </div>
              <button 
                @click="sendFriendRequestToUser(user)"
                class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
                :disabled="sending"
              >
                发送请求
              </button>
            </div>
          </div>
        </div>

        <!-- 通知状态 -->
        <div class="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 class="font-semibold text-gray-900 mb-3">通知状态</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-gray-600">好友请求:</span>
              <span class="font-medium ml-1">{{ notificationsStore.friendRequests.length }}</span>
            </div>
            <div>
              <span class="text-gray-600">系统通知:</span>
              <span class="font-medium ml-1">{{ notificationsStore.notifications.length }}</span>
            </div>
            <div>
              <span class="text-gray-600">未读通知:</span>
              <span class="font-medium ml-1">{{ notificationsStore.unreadCount }}</span>
            </div>
            <div>
              <span class="text-gray-600">总未读:</span>
              <span class="font-medium ml-1">{{ notificationsStore.totalUnreadCount }}</span>
            </div>
          </div>
        </div>

        <!-- 事件日志 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="flex justify-between items-center mb-3">
            <h3 class="font-semibold text-gray-900">事件日志</h3>
            <button 
              @click="clearLogs"
              class="text-sm text-gray-500 hover:text-gray-700"
            >
              清空日志
            </button>
          </div>
          <div class="h-40 overflow-y-auto bg-white p-2 rounded text-xs font-mono">
            <div v-for="(log, index) in eventLogs" :key="index" class="mb-1">
              <span class="text-gray-500">{{ log.time }}</span>
              <span :class="log.type === 'error' ? 'text-red-600' : log.type === 'success' ? 'text-green-600' : 'text-blue-600'">
                {{ log.message }}
              </span>
            </div>
            <div v-if="eventLogs.length === 0" class="text-gray-400 text-center py-4">
              暂无日志
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useNotificationsStore } from '@/stores/notifications'
import { socketService } from '@/services/socket'
import { friendsAPI } from '@/services/api'
import NotificationIcon from '@/components/NotificationIcon.vue'
import type { User } from '@/types'

const authStore = useAuthStore()
const notificationsStore = useNotificationsStore()

const socketConnected = ref(false)
const socketId = ref('')
const targetUsername = ref('')
const sending = ref(false)
const searchResults = ref<User[]>([])
const eventLogs = ref<Array<{time: string, message: string, type: string}>>([])

const addLog = (message: string, type: string = 'info') => {
  const time = new Date().toLocaleTimeString()
  eventLogs.value.unshift({ time, message, type })
  if (eventLogs.value.length > 100) {
    eventLogs.value = eventLogs.value.slice(0, 100)
  }
  console.log(`[MultiUserTest] ${message}`)
}

const updateSocketStatus = () => {
  socketConnected.value = socketService.isConnected()
  socketId.value = socketService.id || ''
}

const testSocketConnection = () => {
  addLog('测试Socket连接状态...', 'info')
  updateSocketStatus()
  
  if (socketConnected.value) {
    addLog(`Socket已连接，ID: ${socketId.value}`, 'success')
    addLog(`当前用户房间: user_${authStore.user?.id}`, 'info')
  } else {
    addLog('Socket未连接', 'error')
  }
}

const reconnectSocket = async () => {
  addLog('重新连接Socket...', 'info')
  
  socketService.disconnect()
  addLog('Socket已断开', 'info')
  
  setTimeout(async () => {
    try {
      await socketService.connect()
      updateSocketStatus()
      addLog('Socket重新连接成功', 'success')
      setupEventListeners()
    } catch (error) {
      addLog(`Socket重连失败: ${error}`, 'error')
    }
  }, 1000)
}

const setupEventListeners = () => {
  addLog('设置事件监听器...', 'info')
  
  // 清理之前的监听器
  socketService.off('friend_request_received')
  socketService.off('friend_request_accepted')
  socketService.off('friend_request_declined')
  
  // 设置新的监听器
  socketService.on('friend_request_received', (data) => {
    addLog(`收到好友请求: ${data.request?.sender_username}`, 'success')
    addLog(`请求数据: ${JSON.stringify(data.request)}`, 'info')
  })
  
  socketService.on('friend_request_accepted', (data) => {
    addLog(`好友请求被接受: ${data.request?.receiver_username}`, 'success')
  })
  
  socketService.on('friend_request_declined', (data) => {
    addLog(`好友请求被拒绝: ${data.request?.receiver_username}`, 'error')
  })
  
  addLog('事件监听器设置完成', 'success')
}

const searchUsers = async () => {
  if (!targetUsername.value.trim()) {
    addLog('请输入用户名进行搜索', 'error')
    return
  }
  
  try {
    addLog(`搜索用户: ${targetUsername.value}`, 'info')
    const response = await friendsAPI.searchUsers({ q: targetUsername.value })
    searchResults.value = response.data.users
    addLog(`找到 ${searchResults.value.length} 个用户`, 'success')
  } catch (error) {
    addLog(`搜索失败: ${error}`, 'error')
  }
}

const sendFriendRequest = async () => {
  if (!targetUsername.value.trim()) {
    addLog('请输入目标用户名', 'error')
    return
  }
  
  // 先搜索用户
  await searchUsers()
  
  if (searchResults.value.length > 0) {
    await sendFriendRequestToUser(searchResults.value[0])
  } else {
    addLog('未找到目标用户', 'error')
  }
}

const sendFriendRequestToUser = async (user: User) => {
  if (sending.value) return
  
  sending.value = true
  
  try {
    addLog(`向 ${user.username} (ID: ${user.id}) 发送好友请求...`, 'info')
    
    const response = await friendsAPI.sendFriendRequest({
      receiver_id: user.id,
      message: `来自多用户测试的好友请求 - ${new Date().toLocaleTimeString()}`
    })
    
    addLog(`好友请求发送成功`, 'success')
    addLog(`响应数据: ${JSON.stringify(response.data)}`, 'info')
    
    // 清空搜索结果
    searchResults.value = []
    targetUsername.value = ''
    
  } catch (error: any) {
    addLog(`发送好友请求失败: ${error.response?.data?.error || error.message}`, 'error')
  } finally {
    sending.value = false
  }
}

const clearLogs = () => {
  eventLogs.value = []
}

onMounted(() => {
  addLog('多用户通知测试页面已加载', 'info')
  updateSocketStatus()
  setupEventListeners()
  
  // 设置状态更新定时器
  const statusInterval = setInterval(updateSocketStatus, 2000)
  
  onUnmounted(() => {
    clearInterval(statusInterval)
  })
})
</script>

<style scoped>
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.btn-secondary {
  @apply px-4 py-2 bg-white/80 text-gray-700 rounded-lg hover:bg-white/90 transition-colors border border-gray-200;
}
</style>
