<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- 顶部导航栏 -->
    <nav class="glass border-b border-white/30 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <RouterLink to="/" class="text-xl font-bold text-gradient">
            1v1 Chat
          </RouterLink>
          <span class="text-sm text-gray-600">好友管理</span>
        </div>
        
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-700">
            {{ authStore.user?.username }}
          </span>
          
          <RouterLink to="/chat" class="btn-secondary text-sm">
            返回聊天
          </RouterLink>

          <!-- 测试通知按钮 -->
          <button
            @click="testNotification"
            class="btn-primary text-sm"
            title="测试通知功能"
          >
            测试通知
          </button>

          <!-- 测试Socket事件按钮 -->
          <button
            @click="testSocketEvent"
            class="btn-secondary text-sm"
            title="测试Socket.IO事件接收"
          >
            测试Socket事件
          </button>

          <!-- 测试真实Socket发送按钮 -->
          <button
            @click="testRealSocketSend"
            class="btn-warning text-sm"
            title="测试真实Socket.IO发送"
          >
            测试真实Socket发送
          </button>

          <!-- 测试Socket连接按钮 -->
          <button
            @click="testSocketConnection"
            class="btn-info text-sm"
            title="测试Socket.IO连接状态"
          >
            测试Socket连接
          </button>

          <button
            @click="handleLogout"
            class="btn-secondary text-sm"
          >
            登出
          </button>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto px-6 py-8">
      <!-- 标签页导航 -->
      <div class="glass rounded-lg mb-6">
        <nav class="flex space-x-8 px-6 py-4">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'py-2 px-1 border-b-2 font-medium text-sm transition-colors',
              activeTab === tab.id
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            {{ tab.name }}
          </button>
        </nav>
      </div>

      <!-- 标签页内容 -->
      <div class="space-y-6">
        <!-- 搜索用户 -->
        <div v-if="activeTab === 'search'">
          <UserSearch
            @start-chat="handleStartChat"
            @friend-request-sent="handleFriendRequestSent"
          />
        </div>

        <!-- 好友管理 -->
        <div v-if="activeTab === 'friends'">
          <FriendManagement
            ref="friendManagementRef"
            :online-users="chatStore.onlineUsers"
            @start-chat="handleStartChat"
          />
        </div>

        <!-- 群组管理 -->
        <div v-if="activeTab === 'groups'">
          <GroupManagement
            @join-group-chat="handleJoinGroupChat"
          />
        </div>

        <!-- 隐私设置 -->
        <div v-if="activeTab === 'privacy'">
          <PrivacySettings
            @change-password="handleChangePassword"
            @export-data="handleExportData"
            @delete-account="handleDeleteAccount"
          />
        </div>
      </div>
    </div>

    <!-- 通知消息 -->
    <div
      v-if="notification"
      class="fixed top-4 right-4 glass rounded-lg p-4 shadow-lg z-50"
      :class="notification.type === 'success' ? 'border-green-200' : 'border-red-200'"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg
            v-if="notification.type === 'success'"
            class="h-5 w-5 text-green-400"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          <svg
            v-else
            class="h-5 w-5 text-red-400"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium" :class="notification.type === 'success' ? 'text-green-800' : 'text-red-800'">
            {{ notification.message }}
          </p>
        </div>
        <div class="ml-auto pl-3">
          <button
            @click="notification = null"
            class="inline-flex text-gray-400 hover:text-gray-600"
          >
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chat'
import { useNotificationsStore } from '@/stores/notifications'
import { socketService } from '@/services/socket'
import { friendsAPI } from '@/services/api'
import UserSearch from '@/components/UserSearch.vue'
import FriendManagement from '@/components/FriendManagement.vue'
import GroupManagement from '@/components/GroupManagement.vue'
import PrivacySettings from '@/components/PrivacySettings.vue'
import type { User, Group } from '@/types'

const router = useRouter()
const authStore = useAuthStore()
const chatStore = useChatStore()
const notificationsStore = useNotificationsStore()

// 组件引用
const friendManagementRef = ref<InstanceType<typeof FriendManagement> | null>(null)

const activeTab = ref('search')
const notification = ref<{ type: 'success' | 'error', message: string } | null>(null)

const tabs = [
  { id: 'search', name: '搜索用户' },
  { id: 'friends', name: '好友管理' },
  { id: 'groups', name: '群组管理' },
  { id: 'privacy', name: '隐私设置' }
]

onMounted(async () => {
  // 确保用户已登录
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  // 确保Socket.IO已连接（如果没有连接则连接）
  await ensureSocketConnection()

  // 设置好友请求事件监听
  setupFriendRequestListeners()

  // 加载现有的好友请求
  await loadExistingFriendRequests()
})

onUnmounted(() => {
  // 清理Socket.IO事件监听器
  cleanupSocketListeners()
})

// 确保Socket.IO连接
const ensureSocketConnection = async () => {
  if (socketService.isConnected()) {
    console.log('Socket already connected for Friends page')
    return
  }

  try {
    console.log('Connecting Socket.IO for Friends page...')
    await socketService.connect()
    console.log('Socket.IO connected successfully for Friends page')
  } catch (error) {
    console.error('Failed to connect Socket.IO for Friends page:', error)
  }
}

// 清理Socket事件监听器
const cleanupSocketListeners = () => {
  console.log('Cleaning up Friends page socket listeners...')
  socketService.off('friend_request_received')
  socketService.off('friend_request_accepted')
  socketService.off('friend_request_declined')
  socketService.off('friend_removed')
}

// 设置好友请求相关的事件监听（Friends页面特有的UI反馈）
const setupFriendRequestListeners = () => {
  console.log('Setting up Friends page socket listeners...')

  // 先清理之前的监听器，避免重复绑定
  cleanupSocketListeners()

  // 监听好友被删除
  socketService.on('friend_removed', (data) => {
    console.log('Friend removed in Friends.vue:', data)
    if (data.removed_by === 'other') {
      showNotification('error', `${data.username} 删除了你的好友关系`)
    } else {
      showNotification('success', `已删除好友 ${data.username}`)
    }
    // 刷新好友管理组件数据，而不是重新加载整个页面
    if (friendManagementRef.value) {
      console.log('Refreshing friend management data...')
      friendManagementRef.value.refreshData()
    } else {
      console.log('friendManagementRef is null, cannot refresh data')
    }
  })

  // 监听好友请求相关事件并显示网页内通知
  socketService.on('friend_request_received', (data) => {
    console.log('Friends page: Friend request received:', data)
    showNotification('success', `收到来自 ${data.request.sender_username} 的好友请求`)
  })

  socketService.on('friend_request_accepted', (data) => {
    console.log('Friends page: Friend request accepted:', data)
    showNotification('success', `${data.request.receiver_username} 接受了你的好友请求`)
  })

  socketService.on('friend_request_declined', (data) => {
    console.log('Friends page: Friend request declined:', data)
    showNotification('error', `${data.request.receiver_username} 拒绝了你的好友请求`)
  })
}

// 加载现有的好友请求
const loadExistingFriendRequests = async () => {
  try {
    console.log('Loading existing friend requests...')
    const response = await friendsAPI.getFriendRequests({
      type: 'received'
    })

    // 将现有的好友请求添加到通知store中
    response.data.requests.forEach((request: any) => {
      notificationsStore.addFriendRequest(request)
    })
    console.log('Loaded existing friend requests:', response.data.requests.length)
  } catch (error: any) {
    console.error('Failed to load existing friend requests:', error)
    // 如果是认证错误，可能需要重新登录
    if (error.response?.status === 401) {
      console.warn('Authentication failed, user may need to login again')
    }
  }
}

const handleStartChat = (user: User) => {
  // 跳转到聊天页面并开始与指定用户聊天
  router.push(`/chat/${user.id}`)
}

const handleJoinGroupChat = (group: Group) => {
  // 跳转到群组聊天页面
  router.push(`/group-chat/${group.id}`)
}

const handleFriendRequestSent = (userId: number) => {
  showNotification('success', '好友请求已发送')
}

const handleChangePassword = () => {
  // 实现密码修改功能
  showNotification('success', '密码修改功能开发中...')
}

const handleExportData = () => {
  // 实现数据导出功能
  showNotification('success', '数据导出功能开发中...')
}

const handleDeleteAccount = () => {
  // 实现账户删除功能
  if (confirm('确定要删除账户吗？此操作不可撤销！')) {
    showNotification('error', '账户删除功能开发中...')
  }
}

const testNotification = async () => {
  console.log('Testing notification functionality...')

  // 测试网页内通知
  showNotification('success', '这是一个测试通知！')

  // 测试通知store和通知图标
  try {
    const testRequest = {
      id: Date.now(),
      sender_id: 999,
      sender_username: '测试用户',
      sender_display_name: '测试用户',
      receiver_id: authStore.user?.id || 1,
      receiver_username: authStore.user?.username || '当前用户',
      receiver_display_name: authStore.user?.display_name || '当前用户',
      status: 'pending' as const,
      message: '这是一个测试好友请求',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    notificationsStore.addFriendRequest(testRequest)
    console.log('✅ Notification store test completed')
    showNotification('success', '通知测试完成！检查右上角通知图标')
  } catch (error) {
    console.error('❌ Notification store test failed:', error)
    showNotification('error', '通知测试失败')
  }
}

const testSocketEvent = async () => {
  console.log('🔧 Testing Socket.IO event reception...')

  // 模拟接收到真实的Socket.IO事件
  const mockSocketData = {
    request: {
      id: Date.now(),
      sender_id: 888,
      sender_username: 'Socket测试用户',
      sender_display_name: 'Socket测试用户',
      receiver_id: authStore.user?.id || 1,
      receiver_username: authStore.user?.username || '当前用户',
      receiver_display_name: authStore.user?.display_name || '当前用户',
      status: 'pending' as const,
      message: '这是一个模拟Socket.IO事件的好友请求',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  }

  console.log('🔧 Simulating friend_request_received event with data:', mockSocketData)
  console.log('🔧 Socket connected:', socketService.isConnected())
  console.log('🔧 Current user:', authStore.user)

  // 手动触发App.vue中的friend_request_received处理逻辑
  try {
    console.log('📝 Adding friend request to notifications store...')
    notificationsStore.addFriendRequest(mockSocketData.request)

    console.log('✅ Socket event simulation completed')
    console.log('✅ Total unread count:', notificationsStore.totalUnreadCount)
    showNotification('success', 'Socket事件测试完成！检查右上角通知图标')
  } catch (error) {
    console.error('❌ Socket event simulation failed:', error)
    showNotification('error', 'Socket事件测试失败')
  }
}

const testRealSocketSend = async () => {
  console.log('🚀 Testing real Socket.IO friend request sending...')

  if (!socketService.isConnected()) {
    console.error('❌ Socket not connected')
    showNotification('error', 'Socket未连接')
    return
  }

  if (!authStore.user) {
    console.error('❌ User not authenticated')
    showNotification('error', '用户未认证')
    return
  }

  // 使用Socket.IO发送好友请求（这会触发后端的Socket处理器）
  const testData = {
    receiver_id: authStore.user.id === 1 ? 3 : 1, // 发送给另一个用户
    message: `Socket.IO测试好友请求 - ${new Date().toLocaleTimeString()}`
  }

  console.log('🚀 Sending friend request via Socket.IO:', testData)
  console.log('🚀 Current user ID:', authStore.user.id)
  console.log('🚀 Target receiver ID:', testData.receiver_id)

  try {
    // 发送Socket.IO事件到后端
    socketService.socketInstance?.emit('send_friend_request', testData)

    console.log('✅ Socket.IO friend request sent')
    showNotification('success', 'Socket.IO好友请求已发送！检查后端日志和通知')
  } catch (error) {
    console.error('❌ Failed to send Socket.IO friend request:', error)
    showNotification('error', 'Socket.IO好友请求发送失败')
  }
}

const testSocketConnection = async () => {
  console.log('🔍 Testing Socket.IO connection status...')

  console.log('🔍 Socket connected:', socketService.isConnected())
  console.log('🔍 Socket instance:', socketService.socketInstance)
  console.log('🔍 Socket ID:', socketService.socketInstance?.id)
  console.log('🔍 Current user:', authStore.user)
  console.log('🔍 User authenticated:', authStore.isAuthenticated)

  if (socketService.isConnected()) {
    // 设置test_pong监听器
    socketService.once('test_pong', (data) => {
      console.log('🔍 Received test pong from server:', data)
      showNotification('success', `收到服务器pong回应: ${data.message}`)
    })

    // 发送一个简单的测试事件
    console.log('🔍 Sending test ping event...')
    socketService.socketInstance?.emit('test_ping', {
      message: 'Test ping from frontend',
      timestamp: new Date().toISOString(),
      user_id: authStore.user?.id
    })

    showNotification('success', 'Socket连接正常！已发送测试ping')
  } else {
    showNotification('error', 'Socket未连接！')

    // 尝试重新连接
    console.log('🔍 Attempting to reconnect...')
    try {
      await socketService.connect()
      showNotification('success', 'Socket重连成功！')
    } catch (error) {
      console.error('❌ Failed to reconnect:', error)
      showNotification('error', 'Socket重连失败')
    }
  }
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('Failed to logout:', error)
  }
}

const showNotification = (type: 'success' | 'error', message: string) => {
  notification.value = { type, message }
  setTimeout(() => {
    notification.value = null
  }, 3000)
}
</script>
