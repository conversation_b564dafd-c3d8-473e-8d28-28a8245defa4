from datetime import datetime
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import or_, and_

from . import friends_bp
from models import db, User, FriendRequest, BlockedUser, FriendRequestRejectCount, get_local_time

@friends_bp.route('/search', methods=['GET'])
@jwt_required()
def search_users():
    """搜索用户"""
    current_user_id = int(get_jwt_identity())
    query = request.args.get('q', '').strip()
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    if not query or len(query) < 2:
        return jsonify({'error': 'Search query must be at least 2 characters'}), 400
    
    current_user = User.query.get(current_user_id)
    if not current_user:
        return jsonify({'error': 'User not found'}), 404
    
    # 基础查询 - 搜索用户名、邮箱或显示名称
    base_query = User.query.filter(
        User.id != current_user_id,  # 排除当前用户
        User.banned == False,  # 排除被封禁用户
        or_(
            User.username.contains(query),
            User.email.contains(query) if query else False,
            User.display_name.contains(query) if query else False
        )
    )
    
    # 根据隐私设置过滤结果
    filtered_users = []
    for user in base_query.all():
        # 检查是否被当前用户屏蔽或屏蔽了当前用户
        if current_user.has_blocked(user.id) or user.has_blocked(current_user_id):
            continue
            
        # 根据隐私设置决定是否显示
        if user.privacy_setting == 'public':
            filtered_users.append(user)
        elif user.privacy_setting == 'friends_only':
            if current_user.is_friend_with(user.id):
                filtered_users.append(user)
        # private 用户不在搜索结果中显示
    
    # 手动分页
    start = (page - 1) * per_page
    end = start + per_page
    paginated_users = filtered_users[start:end]
    
    # 为每个用户添加关系状态
    results = []
    for user in paginated_users:
        user_dict = user.to_dict()
        
        # 检查好友关系状态
        friend_request = FriendRequest.query.filter(
            or_(
                and_(FriendRequest.sender_id == current_user_id, FriendRequest.receiver_id == user.id),
                and_(FriendRequest.sender_id == user.id, FriendRequest.receiver_id == current_user_id)
            )
        ).first()
        
        if friend_request:
            if friend_request.status == 'accepted':
                user_dict['relationship_status'] = 'friend'
            elif friend_request.status == 'pending':
                if friend_request.sender_id == current_user_id:
                    user_dict['relationship_status'] = 'request_sent'
                else:
                    user_dict['relationship_status'] = 'request_received'
            else:
                user_dict['relationship_status'] = 'none'
        else:
            user_dict['relationship_status'] = 'none'
        
        results.append(user_dict)
    
    return jsonify({
        'users': results,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': len(filtered_users),
            'pages': (len(filtered_users) + per_page - 1) // per_page,
            'has_next': end < len(filtered_users),
            'has_prev': page > 1
        }
    })

@friends_bp.route('/requests', methods=['POST'])
@jwt_required()
def send_friend_request():
    """发送好友请求"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    receiver_id = data.get('receiver_id')
    message = data.get('message', '').strip()
    
    if not receiver_id:
        return jsonify({'error': 'receiver_id is required'}), 400
    
    if receiver_id == current_user_id:
        return jsonify({'error': 'Cannot send friend request to yourself'}), 400
    
    # 检查接收者是否存在
    receiver = User.query.get(receiver_id)
    if not receiver or receiver.banned:
        return jsonify({'error': 'User not found or banned'}), 404
    
    current_user = User.query.get(current_user_id)
    
    # 检查是否被屏蔽
    if current_user.has_blocked(receiver_id) or receiver.has_blocked(current_user_id):
        return jsonify({'error': 'Cannot send friend request'}), 403
    
    # 检查拒绝次数限制
    from models import FriendRequestRejectCount
    reject_count_record = FriendRequestRejectCount.query.filter_by(
        sender_id=current_user_id,
        receiver_id=receiver_id
    ).first()

    if reject_count_record and reject_count_record.is_blocked():
        return jsonify({'error': '您已被该用户拒绝多次，无法继续发送好友请求'}), 403

    # 检查是否已经是好友或有待处理的请求
    existing_request = FriendRequest.query.filter(
        or_(
            and_(FriendRequest.sender_id == current_user_id, FriendRequest.receiver_id == receiver_id),
            and_(FriendRequest.sender_id == receiver_id, FriendRequest.receiver_id == current_user_id)
        )
    ).first()

    # 检查当前用户作为发送者的请求
    my_request = FriendRequest.query.filter_by(
        sender_id=current_user_id,
        receiver_id=receiver_id
    ).first()

    if existing_request:
        if existing_request.status == 'accepted':
            return jsonify({'error': 'Already friends'}), 400
        elif existing_request.status == 'pending':
            return jsonify({'error': 'Friend request already pending'}), 400
        elif existing_request.status == 'declined':
            # 如果之前的请求被拒绝，删除旧记录并创建新的请求
            db.session.delete(existing_request)
            db.session.flush()  # 确保删除操作立即执行

            friend_request = FriendRequest(
                sender_id=current_user_id,
                receiver_id=receiver_id,
                message=message[:500] if message else None
            )
            db.session.add(friend_request)
            print(f"Deleted declined request and created new friend request from {current_user.username} to {receiver.username}")
        else:
            return jsonify({'error': 'Invalid request status'}), 400
    else:
        # 创建新的好友请求
        friend_request = FriendRequest(
            sender_id=current_user_id,
            receiver_id=receiver_id,
            message=message[:500] if message else None  # 限制消息长度
        )
        db.session.add(friend_request)
        print(f"Created new friend request from {current_user.username} to {receiver.username}")

    try:
        db.session.commit()

        # 通过Socket.IO实时通知接收者
        try:
            from chat.socket_handlers import get_socketio, online_users
            socketio = get_socketio()

            if socketio:
                room_name = f"user_{receiver_id}"
                request_data = friend_request.to_dict()

                print(f"🔔 Sending friend request notification:")
                print(f"  - From: {current_user.username} (ID: {current_user_id})")
                print(f"  - To: {receiver.username} (ID: {receiver_id})")
                print(f"  - Room: {room_name}")
                print(f"  - Request data: {request_data}")

                # 检查接收者是否在线
                receiver_online = any(user_info['user_id'] == receiver_id for user_info in online_users.values())
                print(f"  - Receiver online: {receiver_online}")
                if receiver_online:
                    receiver_sessions = [sid for sid, user_info in online_users.items() if user_info['user_id'] == receiver_id]
                    print(f"  - Receiver sessions: {receiver_sessions}")

                # 发送好友请求通知给接收者
                socketio.emit('friend_request_received', {
                    'request': request_data
                }, room=room_name)

                print(f"✅ Friend request notification sent to user {receiver_id} in room {room_name}")
            else:
                print("❌ SocketIO instance not available")
        except Exception as e:
            print(f"❌ Failed to send Socket.IO notification: {e}")
            import traceback
            traceback.print_exc()

        return jsonify({
            'message': 'Friend request sent successfully',
            'request': friend_request.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        print(f"Error sending friend request: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': 'Failed to send friend request'}), 500

@friends_bp.route('/requests', methods=['GET'])
@jwt_required()
def get_friend_requests():
    """获取好友请求列表"""
    current_user_id = int(get_jwt_identity())
    request_type = request.args.get('type', 'received')  # received, sent, all
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # 根据请求类型决定查询状态
    if request_type == 'received':
        # 接收的请求只显示pending状态
        query = FriendRequest.query.filter_by(receiver_id=current_user_id, status='pending')
    elif request_type == 'sent':
        # 发送的请求显示pending和declined状态
        query = FriendRequest.query.filter_by(sender_id=current_user_id).filter(
            FriendRequest.status.in_(['pending', 'declined'])
        )
    elif request_type == 'all':
        # 所有请求显示pending状态，以及当前用户发送的declined状态
        query = FriendRequest.query.filter(
            or_(
                and_(FriendRequest.receiver_id == current_user_id, FriendRequest.status == 'pending'),
                and_(FriendRequest.sender_id == current_user_id, FriendRequest.status.in_(['pending', 'declined']))
            )
        )
    else:
        # 默认只显示pending状态
        query = FriendRequest.query.filter_by(status='pending')
    
    requests = query.order_by(FriendRequest.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'requests': [req.to_dict() for req in requests.items],
        'pagination': {
            'page': page,
            'pages': requests.pages,
            'per_page': per_page,
            'total': requests.total,
            'has_next': requests.has_next,
            'has_prev': requests.has_prev
        }
    })

@friends_bp.route('/requests/<int:request_id>/accept', methods=['PUT'])
@jwt_required()
def accept_friend_request(request_id):
    """接受好友请求"""
    current_user_id = int(get_jwt_identity())
    
    friend_request = FriendRequest.query.get(request_id)
    if not friend_request:
        return jsonify({'error': 'Friend request not found'}), 404
    
    if friend_request.receiver_id != current_user_id:
        return jsonify({'error': 'Not authorized to accept this request'}), 403
    
    if friend_request.status != 'pending':
        return jsonify({'error': 'Request is not pending'}), 400
    
    try:
        # 接受好友请求
        friend_request.accept()

        # 获取用户信息
        from models import Message, get_local_time

        receiver = User.query.get(current_user_id)
        sender = User.query.get(friend_request.sender_id)

        if not receiver or not sender:
            return jsonify({'error': 'User not found'}), 404

        # 创建系统消息通知好友请求已被接受
        system_message_content = f"你们现在是好友了！{receiver.username} 接受了 {sender.username} 的好友请求。"

        # 发送给请求发送者的系统消息
        system_message_to_sender = Message(
            content=system_message_content,
            sender_id=current_user_id,  # 接受者作为发送者
            recipient_id=friend_request.sender_id,
            timestamp=get_local_time()
        )
        db.session.add(system_message_to_sender)

        # 发送给请求接受者的系统消息
        system_message_to_receiver = Message(
            content=system_message_content,
            sender_id=friend_request.sender_id,  # 发送者作为发送者
            recipient_id=current_user_id,
            timestamp=get_local_time()
        )
        db.session.add(system_message_to_receiver)

        db.session.commit()

        # 通过Socket.IO发送新对话事件
        try:
            from chat.socket_handlers import get_socketio
            socketio = get_socketio()

            if socketio:
                # 获取系统消息数据
                sender_message_data = system_message_to_sender.to_dict()
                receiver_message_data = system_message_to_receiver.to_dict()

                # 发送新对话通知给请求发送者
                socketio.emit('new_conversation', {
                    'user': receiver.to_dict(),
                    'message': sender_message_data
                }, room=f"user_{friend_request.sender_id}")

                # 发送新对话通知给请求接受者
                socketio.emit('new_conversation', {
                    'user': sender.to_dict(),
                    'message': receiver_message_data
                }, room=f"user_{current_user_id}")

                print(f"Friend request accepted: system messages and new conversation notifications sent to both {sender.username} and {receiver.username}")
        except Exception as e:
            print(f"Failed to send Socket.IO notifications: {e}")

        return jsonify({
            'message': 'Friend request accepted',
            'request': friend_request.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to accept friend request'}), 500

@friends_bp.route('/requests/<int:request_id>/decline', methods=['PUT'])
@jwt_required()
def decline_friend_request(request_id):
    """拒绝好友请求"""
    current_user_id = int(get_jwt_identity())
    
    friend_request = FriendRequest.query.get(request_id)
    if not friend_request:
        return jsonify({'error': 'Friend request not found'}), 404
    
    if friend_request.receiver_id != current_user_id:
        return jsonify({'error': 'Not authorized to decline this request'}), 403
    
    if friend_request.status != 'pending':
        return jsonify({'error': 'Request is not pending'}), 400
    
    try:
        friend_request.decline()

        # 记录拒绝次数
        from models import FriendRequestRejectCount
        reject_count_record = FriendRequestRejectCount.query.filter_by(
            sender_id=friend_request.sender_id,
            receiver_id=friend_request.receiver_id
        ).first()

        if reject_count_record:
            reject_count_record.increment_reject_count()
        else:
            reject_count_record = FriendRequestRejectCount(
                sender_id=friend_request.sender_id,
                receiver_id=friend_request.receiver_id,
                reject_count=1,
                last_rejected_at=datetime.utcnow()
            )
            db.session.add(reject_count_record)

        db.session.commit()

        # 通过Socket.IO通知发送者请求被拒绝
        try:
            from chat.socket_handlers import get_socketio
            socketio = get_socketio()

            if socketio:
                # 发送拒绝通知给请求发送者
                socketio.emit('friend_request_declined', {
                    'request': friend_request.to_dict()
                }, room=f"user_{friend_request.sender_id}")

                print(f"Friend request declined notification sent to user {friend_request.sender_id}")
        except Exception as e:
            print(f"Failed to send Socket.IO notification: {e}")

        return jsonify({
            'message': 'Friend request declined',
            'request': friend_request.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to decline friend request'}), 500

@friends_bp.route('/list', methods=['GET'])
@jwt_required()
def get_friends():
    """获取好友列表"""
    current_user_id = int(get_jwt_identity())
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    
    current_user = User.query.get(current_user_id)
    if not current_user:
        return jsonify({'error': 'User not found'}), 404
    
    friends = current_user.get_friends()
    
    # 手动分页
    start = (page - 1) * per_page
    end = start + per_page
    paginated_friends = friends[start:end]
    
    return jsonify({
        'friends': [friend.to_dict() for friend in paginated_friends],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': len(friends),
            'pages': (len(friends) + per_page - 1) // per_page,
            'has_next': end < len(friends),
            'has_prev': page > 1
        }
    })

@friends_bp.route('/<int:friend_id>', methods=['DELETE'])
@jwt_required()
def remove_friend(friend_id):
    """移除好友"""
    current_user_id = int(get_jwt_identity())

    if friend_id == current_user_id:
        return jsonify({'error': 'Cannot remove yourself'}), 400

    # 查找好友关系
    friend_request = FriendRequest.query.filter(
        or_(
            and_(FriendRequest.sender_id == current_user_id, FriendRequest.receiver_id == friend_id),
            and_(FriendRequest.sender_id == friend_id, FriendRequest.receiver_id == current_user_id)
        ),
        FriendRequest.status == 'accepted'
    ).first()

    if not friend_request:
        return jsonify({'error': 'Friend relationship not found'}), 404

    try:
        # 获取用户信息用于通知
        from models import User
        current_user = User.query.get(current_user_id)
        friend_user = User.query.get(friend_id)

        db.session.delete(friend_request)
        db.session.commit()

        # 通过Socket.IO实时通知双方好友关系已删除
        try:
            from chat.socket_handlers import get_socketio
            socketio = get_socketio()

            if socketio and current_user and friend_user:
                # 通知当前用户
                socketio.emit('friend_removed', {
                    'user_id': friend_id,
                    'username': friend_user.username,
                    'removed_by': 'self'
                }, room=f"user_{current_user_id}")

                # 通知被删除的好友
                socketio.emit('friend_removed', {
                    'user_id': current_user_id,
                    'username': current_user.username,
                    'removed_by': 'other'
                }, room=f"user_{friend_id}")

                print(f"Friend removal notifications sent between {current_user.username} and {friend_user.username}")
        except Exception as e:
            print(f"Failed to send Socket.IO notifications: {e}")

        return jsonify({'message': 'Friend removed successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to remove friend'}), 500

@friends_bp.route('/block', methods=['POST'])
@jwt_required()
def block_user():
    """屏蔽用户"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        return jsonify({'error': 'No data provided'}), 400

    blocked_user_id = data.get('user_id')

    if not blocked_user_id:
        return jsonify({'error': 'user_id is required'}), 400

    if blocked_user_id == current_user_id:
        return jsonify({'error': 'Cannot block yourself'}), 400

    # 检查被屏蔽用户是否存在
    blocked_user = User.query.get(blocked_user_id)
    if not blocked_user:
        return jsonify({'error': 'User not found'}), 404

    # 检查是否已经屏蔽
    existing_block = BlockedUser.query.filter_by(
        blocker_id=current_user_id,
        blocked_id=blocked_user_id
    ).first()

    if existing_block:
        return jsonify({'error': 'User already blocked'}), 400

    try:
        # 如果是好友关系，先移除好友关系
        friend_request = FriendRequest.query.filter(
            or_(
                and_(FriendRequest.sender_id == current_user_id, FriendRequest.receiver_id == blocked_user_id),
                and_(FriendRequest.sender_id == blocked_user_id, FriendRequest.receiver_id == current_user_id)
            ),
            FriendRequest.status == 'accepted'
        ).first()

        if friend_request:
            db.session.delete(friend_request)

        # 创建屏蔽关系
        block_relationship = BlockedUser(
            blocker_id=current_user_id,
            blocked_id=blocked_user_id
        )

        db.session.add(block_relationship)
        db.session.commit()

        return jsonify({
            'message': 'User blocked successfully',
            'blocked_user': block_relationship.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to block user'}), 500

@friends_bp.route('/block/<int:blocked_user_id>', methods=['DELETE'])
@jwt_required()
def unblock_user(blocked_user_id):
    """解除屏蔽用户"""
    current_user_id = int(get_jwt_identity())

    block_relationship = BlockedUser.query.filter_by(
        blocker_id=current_user_id,
        blocked_id=blocked_user_id
    ).first()

    if not block_relationship:
        return jsonify({'error': 'Block relationship not found'}), 404

    try:
        db.session.delete(block_relationship)
        db.session.commit()

        return jsonify({'message': 'User unblocked successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to unblock user'}), 500

@friends_bp.route('/restricted', methods=['GET'])
@jwt_required()
def get_restricted_users():
    """获取被当前用户多次拒绝而限制申请的用户列表"""
    current_user_id = int(get_jwt_identity())
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # 查询被当前用户拒绝3次或以上的用户
    restricted_records = FriendRequestRejectCount.query.filter(
        FriendRequestRejectCount.receiver_id == current_user_id,
        FriendRequestRejectCount.reject_count >= 3
    ).order_by(FriendRequestRejectCount.last_rejected_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    restricted_users = []
    for record in restricted_records.items:
        sender = User.query.get(record.sender_id)
        if sender and not sender.banned:
            restricted_users.append({
                'user': sender.to_dict(),
                'reject_count': record.reject_count,
                'last_rejected_at': record.last_rejected_at.isoformat() if record.last_rejected_at else None,
                'record_id': record.id
            })

    return jsonify({
        'restricted_users': restricted_users,
        'pagination': {
            'page': restricted_records.page,
            'per_page': restricted_records.per_page,
            'total': restricted_records.total,
            'pages': restricted_records.pages,
            'has_next': restricted_records.has_next,
            'has_prev': restricted_records.has_prev
        }
    })

@friends_bp.route('/restricted/<int:user_id>/restore', methods=['PUT'])
@jwt_required()
def restore_user_request_permission(user_id):
    """恢复用户的好友申请权限"""
    current_user_id = int(get_jwt_identity())

    # 查找拒绝记录
    reject_record = FriendRequestRejectCount.query.filter_by(
        sender_id=user_id,
        receiver_id=current_user_id
    ).first()

    if not reject_record:
        return jsonify({'error': 'No restriction record found'}), 404

    if not reject_record.is_blocked():
        return jsonify({'error': 'User is not restricted'}), 400

    try:
        # 重置拒绝次数为0，恢复申请权限
        reject_record.reject_count = 0
        reject_record.last_rejected_at = None
        reject_record.updated_at = datetime.utcnow()

        db.session.commit()

        # 获取用户信息用于返回
        user = User.query.get(user_id)
        user_info = user.to_dict() if user else None

        return jsonify({
            'message': 'User request permission restored successfully',
            'user': user_info,
            'reject_count': reject_record.reject_count
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to restore user permission'}), 500

@friends_bp.route('/blocked', methods=['GET'])
@jwt_required()
def get_blocked_users():
    """获取被屏蔽的用户列表"""
    current_user_id = int(get_jwt_identity())
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    blocked_relationships = BlockedUser.query.filter_by(
        blocker_id=current_user_id
    ).order_by(BlockedUser.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return jsonify({
        'blocked_users': [rel.to_dict() for rel in blocked_relationships.items],
        'pagination': {
            'page': page,
            'pages': blocked_relationships.pages,
            'per_page': per_page,
            'total': blocked_relationships.total,
            'has_next': blocked_relationships.has_next,
            'has_prev': blocked_relationships.has_prev
        }
    })

@friends_bp.route('/privacy', methods=['PUT'])
@jwt_required()
def update_privacy_setting():
    """更新隐私设置"""
    current_user_id = int(get_jwt_identity())
    data = request.get_json()

    if not data:
        return jsonify({'error': 'No data provided'}), 400

    privacy_setting = data.get('privacy_setting')
    allow_stranger_messages = data.get('allow_stranger_messages')

    # 验证隐私设置
    if privacy_setting and privacy_setting not in ['public', 'friends_only', 'private']:
        return jsonify({'error': 'Invalid privacy setting'}), 400

    try:
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # 更新隐私设置
        if privacy_setting:
            user.privacy_setting = privacy_setting

        # 更新陌生人消息设置
        if allow_stranger_messages is not None:
            user.allow_stranger_messages = bool(allow_stranger_messages)

        db.session.commit()

        return jsonify({
            'message': 'Privacy settings updated successfully',
            'user': user.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to update privacy setting'}), 500

@friends_bp.route('/requests/<int:request_id>/mark-read', methods=['PUT'])
@jwt_required()
def mark_friend_request_as_read(request_id):
    """标记好友请求为已读"""
    current_user_id = int(get_jwt_identity())

    try:
        friend_request = FriendRequest.query.get(request_id)
        if not friend_request:
            return jsonify({'error': 'Friend request not found'}), 404

        # 只有接收者可以标记为已读
        if friend_request.receiver_id != current_user_id:
            return jsonify({'error': 'Permission denied'}), 403

        # 标记为已读
        friend_request.is_read = True
        friend_request.updated_at = get_local_time()
        db.session.commit()

        return jsonify({
            'message': 'Friend request marked as read',
            'request': friend_request.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        print(f"Error marking friend request as read: {e}")
        return jsonify({'error': 'Failed to mark friend request as read'}), 500

@friends_bp.route('/requests/mark-all-read', methods=['PUT'])
@jwt_required()
def mark_all_friend_requests_as_read():
    """标记所有好友请求为已读"""
    current_user_id = int(get_jwt_identity())

    try:
        # 获取当前用户收到的所有未读好友请求
        unread_requests = FriendRequest.query.filter_by(
            receiver_id=current_user_id,
            is_read=False
        ).all()

        # 标记所有为已读
        for request in unread_requests:
            request.is_read = True
            request.updated_at = get_local_time()

        db.session.commit()

        return jsonify({
            'message': f'Marked {len(unread_requests)} friend requests as read',
            'count': len(unread_requests)
        })

    except Exception as e:
        db.session.rollback()
        print(f"Error marking all friend requests as read: {e}")
        return jsonify({'error': 'Failed to mark all friend requests as read'}), 500
