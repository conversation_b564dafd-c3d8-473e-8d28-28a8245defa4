from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from . import questionnaire_bp
from models import (
    db, User, Questionnaire, QuestionnairePage, Question, QuestionOption,
    QuestionnaireResponse, Answer, RevealRequest, QuestionType, Message
)
from datetime import datetime
import json
from functools import wraps

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        try:
            user_id = get_jwt_identity()
            if not user_id:
                return jsonify({'error': 'Admin access required'}), 403

            # 确保user_id是整数类型
            if isinstance(user_id, str):
                user_id = int(user_id)

            user = User.query.get(user_id)
            if not user or not user.is_admin:
                return jsonify({'error': 'Admin access required'}), 403

            return f(*args, **kwargs)
        except (ValueError, TypeError) as e:
            return jsonify({'error': 'Invalid user identity'}), 401
        except Exception as e:
            return jsonify({'error': 'Authentication error'}), 500
    return decorated_function

def auth_required(f):
    """用户认证装饰器"""
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        try:
            user_id = get_jwt_identity()
            if not user_id:
                return jsonify({'error': 'Authentication required'}), 401

            # 确保user_id是整数类型
            if isinstance(user_id, str):
                user_id = int(user_id)

            user = User.query.get(user_id)
            if not user:
                return jsonify({'error': 'User not found'}), 401

            return f(*args, **kwargs)
        except (ValueError, TypeError) as e:
            return jsonify({'error': 'Invalid user identity'}), 401
        except Exception as e:
            return jsonify({'error': 'Authentication error'}), 500
    return decorated_function

@questionnaire_bp.route('/create', methods=['POST'])
@admin_required
def create_questionnaire():
    """创建问卷（仅管理员）"""
    
    try:
        data = request.get_json()
        
        # 创建问卷
        user_id = get_jwt_identity()
        if isinstance(user_id, str):
            user_id = int(user_id)
        questionnaire = Questionnaire(
            title=data.get('title'),
            description=data.get('description'),
            creator_id=user_id
        )
        
        db.session.add(questionnaire)
        db.session.flush()  # 获取ID
        
        # 创建页面和题目
        pages_data = data.get('pages', [])
        for page_data in pages_data:
            page = QuestionnairePage(
                questionnaire_id=questionnaire.id,
                title=page_data.get('title'),
                description=page_data.get('description'),
                page_order=page_data.get('page_order', 1)
            )
            db.session.add(page)
            db.session.flush()
            
            # 创建题目
            questions_data = page_data.get('questions', [])
            for question_data in questions_data:
                question = Question(
                    page_id=page.id,
                    question_text=question_data.get('question_text'),
                    question_type=question_data.get('question_type'),
                    is_required=question_data.get('is_required', False),
                    question_order=question_data.get('question_order', 1)
                )
                db.session.add(question)
                db.session.flush()
                
                # 创建选项（如果是选择题）
                if question.question_type in ['single_choice', 'multiple_choice']:
                    options_data = question_data.get('options', [])
                    for option_data in options_data:
                        option = QuestionOption(
                            question_id=question.id,
                            option_text=option_data.get('option_text'),
                            option_order=option_data.get('option_order', 1)
                        )
                        db.session.add(option)
        
        db.session.commit()
        
        return jsonify({
            'message': 'Questionnaire created successfully',
            'questionnaire': questionnaire.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@questionnaire_bp.route('/list', methods=['GET'])
@admin_required
def list_questionnaires():
    """获取问卷列表（仅管理员）"""
    
    try:
        questionnaires = Questionnaire.query.filter_by(is_active=True).all()
        return jsonify({
            'questionnaires': [q.to_dict() for q in questionnaires]
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@questionnaire_bp.route('/<int:questionnaire_id>', methods=['GET'])
@auth_required
def get_questionnaire(questionnaire_id):
    """获取问卷详情"""
    
    try:
        questionnaire = Questionnaire.query.get_or_404(questionnaire_id)
        return jsonify({
            'questionnaire': questionnaire.to_dict()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@questionnaire_bp.route('/<int:questionnaire_id>/update', methods=['PUT'])
@admin_required
def update_questionnaire(questionnaire_id):
    """更新问卷（仅管理员）"""
    
    try:
        questionnaire = Questionnaire.query.get_or_404(questionnaire_id)
        data = request.get_json()
        
        questionnaire.title = data.get('title', questionnaire.title)
        questionnaire.description = data.get('description', questionnaire.description)
        questionnaire.is_active = data.get('is_active', questionnaire.is_active)
        questionnaire.updated_at = datetime.now()
        
        db.session.commit()
        
        return jsonify({
            'message': 'Questionnaire updated successfully',
            'questionnaire': questionnaire.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@questionnaire_bp.route('/<int:questionnaire_id>/delete', methods=['DELETE'])
@admin_required
def delete_questionnaire(questionnaire_id):
    """删除问卷（仅管理员）"""
    
    try:
        questionnaire = Questionnaire.query.get_or_404(questionnaire_id)
        questionnaire.is_active = False
        questionnaire.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'message': 'Questionnaire deleted successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@questionnaire_bp.route('/<int:questionnaire_id>/submit', methods=['POST'])
@auth_required
def submit_questionnaire_response(questionnaire_id):
    """提交问卷回答"""
    
    try:
        data = request.get_json()
        questionnaire = Questionnaire.query.get_or_404(questionnaire_id)
        
        # 创建回答记录
        user_id = get_jwt_identity()
        if isinstance(user_id, str):
            user_id = int(user_id)
        response = QuestionnaireResponse(
            questionnaire_id=questionnaire_id,
            respondent_id=user_id,
            sender_id=data.get('sender_id'),
            message_id=data.get('message_id'),
            is_completed=True,
            submitted_at=datetime.now()
        )
        
        db.session.add(response)
        db.session.flush()
        
        # 保存答案
        answers_data = data.get('answers', [])
        for answer_data in answers_data:
            answer = Answer(
                response_id=response.id,
                question_id=answer_data.get('question_id'),
                selected_option_ids=json.dumps(answer_data.get('selected_option_ids', [])) if answer_data.get('selected_option_ids') else None,
                text_answer=answer_data.get('text_answer'),
                is_hidden=answer_data.get('is_hidden', False)
            )
            db.session.add(answer)
        
        db.session.commit()
        
        return jsonify({
            'message': 'Questionnaire response submitted successfully',
            'response': response.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@questionnaire_bp.route('/response/<int:response_id>', methods=['GET'])
@auth_required
def get_questionnaire_response(response_id):
    """获取问卷回答详情"""
    
    try:
        response = QuestionnaireResponse.query.get_or_404(response_id)
        
        # 检查权限：只有发送者和回答者可以查看
        user_id = get_jwt_identity()
        if isinstance(user_id, str):
            user_id = int(user_id)
        if user_id not in [response.sender_id, response.respondent_id]:
            return jsonify({'error': 'Access denied'}), 403
        
        return jsonify({
            'response': response.to_dict()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@questionnaire_bp.route('/answer/<int:answer_id>/reveal', methods=['POST'])
@auth_required
def reveal_answer(answer_id):
    """揭秘隐藏的答案"""
    
    try:
        answer = Answer.query.get_or_404(answer_id)
        
        # 检查权限：只有回答者可以揭秘自己的答案
        user_id = get_jwt_identity()
        if isinstance(user_id, str):
            user_id = int(user_id)
        if user_id != answer.response.respondent_id:
            return jsonify({'error': 'Only respondent can reveal their own answers'}), 403

        if not answer.is_hidden:
            return jsonify({'error': 'Answer is not hidden'}), 400

        answer.is_revealed = True
        answer.revealed_at = datetime.utcnow()
        answer.revealed_by = user_id
        
        db.session.commit()
        
        return jsonify({
            'message': 'Answer revealed successfully',
            'answer': answer.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@questionnaire_bp.route('/answer/<int:answer_id>/request-reveal', methods=['POST'])
@auth_required
def request_reveal_answer(answer_id):
    """申请揭秘答案"""
    
    try:
        answer = Answer.query.get_or_404(answer_id)
        
        # 检查是否已经申请过
        user_id = get_jwt_identity()
        if isinstance(user_id, str):
            user_id = int(user_id)
        existing_request = RevealRequest.query.filter_by(
            answer_id=answer_id,
            requester_id=user_id,
            status='pending'
        ).first()

        if existing_request:
            return jsonify({'error': '您已申请过，请不要重复申请'}), 400

        # 创建申请
        reveal_request = RevealRequest(
            answer_id=answer_id,
            requester_id=user_id
        )
        
        db.session.add(reveal_request)
        db.session.commit()

        # 发送系统消息通知回答者
        try:
            send_reveal_request_notification(answer, user_id, reveal_request.id)
        except Exception as e:
            print(f"Failed to send reveal request notification: {e}")

        return jsonify({
            'message': 'Reveal request submitted successfully',
            'request': reveal_request.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def send_reveal_request_notification(answer, requester_id, request_id):
    """发送揭秘申请通知消息"""
    try:
        # 获取问题信息
        question = answer.question
        questionnaire = question.page.questionnaire

        # 获取申请者信息
        requester = User.query.get(requester_id)
        if not requester:
            return

        # 创建系统消息
        message_content = f"对方申请让您揭秘隐藏的「{question.question_text}」的答案"

        # 创建消息记录
        message = Message(
            content=message_content,
            message_type='reveal_request',
            sender_id=requester_id,  # 申请者
            recipient_id=answer.response.respondent_id,  # 回答者
            reveal_request_id=request_id
        )

        db.session.add(message)
        db.session.commit()

        # 通过Socket.IO发送实时通知
        try:
            from chat.socket_handlers import socketio_instance
            if socketio_instance:
                message_data = message.to_dict()
                # 发送给回答者
                socketio_instance.emit('new_message', {
                    'message': message_data
                }, room=f"user_{answer.response.respondent_id}")

                print(f"Reveal request notification sent to user {answer.response.respondent_id}")
        except Exception as e:
            print(f"Failed to send real-time notification: {e}")

    except Exception as e:
        print(f"Error sending reveal request notification: {e}")
        db.session.rollback()

@questionnaire_bp.route('/reveal-request/<int:request_id>/approve', methods=['POST'])
@auth_required
def approve_reveal_request(request_id):
    """同意揭秘申请"""

    try:
        reveal_request = RevealRequest.query.get_or_404(request_id)

        # 检查权限：只有回答者可以同意揭秘申请
        user_id = get_jwt_identity()
        if isinstance(user_id, str):
            user_id = int(user_id)
        if user_id != reveal_request.answer.response.respondent_id:
            return jsonify({'error': 'Only respondent can approve reveal requests'}), 403

        if reveal_request.status != 'pending':
            return jsonify({'error': 'Request is not pending'}), 400

        # 更新申请状态
        reveal_request.status = 'approved'
        reveal_request.processed_at = datetime.utcnow()

        # 揭秘答案
        answer = reveal_request.answer
        answer.is_revealed = True
        answer.revealed_at = datetime.utcnow()
        answer.revealed_by = user_id

        db.session.commit()

        return jsonify({
            'message': 'Reveal request approved successfully',
            'request': reveal_request.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@questionnaire_bp.route('/reveal-request/<int:request_id>/reject', methods=['POST'])
@auth_required
def reject_reveal_request(request_id):
    """拒绝揭秘申请"""

    try:
        reveal_request = RevealRequest.query.get_or_404(request_id)

        # 检查权限：只有回答者可以拒绝揭秘申请
        user_id = get_jwt_identity()
        if isinstance(user_id, str):
            user_id = int(user_id)
        if user_id != reveal_request.answer.response.respondent_id:
            return jsonify({'error': 'Only respondent can reject reveal requests'}), 403

        if reveal_request.status != 'pending':
            return jsonify({'error': 'Request is not pending'}), 400

        # 更新申请状态
        reveal_request.status = 'rejected'
        reveal_request.processed_at = datetime.utcnow()

        db.session.commit()

        return jsonify({
            'message': 'Reveal request rejected successfully',
            'request': reveal_request.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
