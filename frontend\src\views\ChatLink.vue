<template>
  <div class="min-h-screen flex items-center justify-center px-6 py-12">
    <div class="max-w-md w-full">
      <!-- 返回首页链接 -->
      <div class="mb-8">
        <RouterLink 
          to="/" 
          class="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          返回首页
        </RouterLink>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="card-glass text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <p class="text-gray-600">验证聊天链接...</p>
      </div>

      <!-- 链接无效 -->
      <div v-else-if="error" class="card-glass text-center">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">链接无效</h2>
        <p class="text-gray-600 mb-6">{{ error }}</p>
        
        <div class="space-y-3">
          <RouterLink to="/" class="btn-primary w-full">
            返回首页
          </RouterLink>
          <RouterLink to="/chat" class="btn-secondary w-full">
            直接进入聊天
          </RouterLink>
        </div>
      </div>

      <!-- 链接有效，显示使用选项 -->
      <div v-else-if="linkData" class="card-glass">
        <div class="text-center mb-6">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-gray-900 mb-2">聊天邀请</h2>
          <p class="text-gray-600">
            {{ linkData?.link?.creator_username || '用户' }} 邀请您开始聊天
          </p>
        </div>

        <!-- 链接信息 -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">创建者:</span>
              <span class="font-bold text-black">{{ linkData?.link?.creator_username || '加载中...' }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">过期时间:</span>
              <span class="font-bold text-black">{{ linkData?.link?.expires_at ? formatExpireTime(linkData.link.expires_at) : '加载中...' }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">使用限制:</span>
              <span class="font-bold text-black">{{ linkData?.link?.is_single_use !== undefined ? (linkData.link.is_single_use ? '单次使用' : '多次使用') : '加载中...' }}</span>
            </div>
          </div>
        </div>

        <!-- 当前用户状态 -->
        <div v-if="authStore.isAuthenticated" class="mb-6">
          <p class="text-sm text-gray-600 mb-3">
            当前用户: {{ authStore.user?.username }}
            <span v-if="authStore.isAnonymous" class="text-gray-500">(匿名)</span>
          </p>
          
          <!-- 不能使用自己的链接 -->
          <div v-if="linkData.link.creator_id === authStore.user?.id" class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg mb-4">
            <p class="text-sm text-yellow-700">这是您自己创建的链接，无法使用。</p>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="space-y-3">
          <button
            v-if="linkData.link.creator_id !== authStore.user?.id"
            @click="useLinkAndStartChat"
            :disabled="processing"
            class="btn-primary w-full"
          >
            {{ processing ? '处理中...' : '开始聊天' }}
          </button>

          <RouterLink to="/chat" class="btn-secondary w-full">
            直接进入聊天室
          </RouterLink>
        </div>

        <!-- 其他选项 -->
        <div v-if="authStore.isAnonymous" class="mt-6 pt-4 border-t border-gray-200 text-center">
          <p class="text-sm text-gray-600 mb-3">想要保存聊天记录？</p>
          <div class="space-y-2">
            <RouterLink to="/login" class="btn-secondary w-full text-sm">
              登录现有账号
            </RouterLink>
            <RouterLink to="/register" class="text-sm text-primary-600 hover:text-primary-700">
              注册新账号
            </RouterLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chat'
import { socketService } from '@/services/socket'
import { formatFullDateTime } from '@/utils/time'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const chatStore = useChatStore()

const loading = ref(true)
const processing = ref(false)
const error = ref('')
const linkData = ref<any>(null)

const code = route.params.code as string

onMounted(async () => {
  console.log('ChatLink component mounted, code:', code)
  console.log('Initial auth state:', {
    isAuthenticated: authStore.isAuthenticated,
    user: authStore.user
  })

  // 如果用户未登录，自动创建匿名用户
  if (!authStore.isAuthenticated) {
    try {
      console.log('Creating anonymous user...')
      await authStore.createAnonymousUser()
      console.log('Anonymous user created for friend link access')
    } catch (error) {
      console.error('Failed to create anonymous user:', error)
      // 即使创建匿名用户失败，也继续验证链接
    }
  }

  await validateLink()
})

// 验证链接
const validateLink = async () => {
  try {
    console.log('Validating chat link:', code)
    const result = await chatStore.validateChatLink(code)
    console.log('Validation result:', result)

    if (result.valid) {
      linkData.value = result
      console.log('Link data set:', linkData.value)
    } else {
      error.value = result.error || '链接无效或已过期'
      console.error('Link validation failed:', result.error)
    }
  } catch (err: any) {
    console.error('Link validation error:', err)
    error.value = err.response?.data?.error || '验证链接时出错'
  } finally {
    loading.value = false
  }
}



// 使用链接并开始聊天
const useLinkAndStartChat = async () => {
  processing.value = true

  try {
    // 确保用户已认证（匿名或正式用户）
    if (!authStore.isAuthenticated) {
      console.log('Creating anonymous user before using chat link')
      await authStore.createAnonymousUser()
      console.log('Anonymous user created, waiting for auth state to update')

      // 等待认证状态更新
      let retries = 0
      while (!authStore.isAuthenticated && retries < 15) {
        await new Promise(resolve => setTimeout(resolve, 200))
        retries++
      }

      if (!authStore.isAuthenticated) {
        throw new Error('Failed to authenticate anonymous user')
      }

      console.log('Anonymous user authentication confirmed')
    }

    console.log('Using chat link with authenticated user:', authStore.user?.username)
    const result = await chatStore.useChatLink(code)

    // 设置当前聊天用户为链接创建者
    console.log('Setting current chat user:', result.creator.username)
    await chatStore.setCurrentChatUser(result.creator)

    // 等待一下确保状态同步
    await new Promise(resolve => setTimeout(resolve, 300))

    // 跳转到聊天页面
    try {
      await router.push(`/chat/${result.creator.id}`)
    } catch (routerError) {
      console.warn('Router navigation error (possibly SVG-related), retrying:', routerError)
      // Retry navigation after a short delay
      setTimeout(() => {
        router.push(`/chat/${result.creator.id}`)
      }, 100)
    }
  } catch (err: any) {
    console.error('Error using chat link:', err)
    error.value = err.response?.data?.error || '使用链接时出错'
    processing.value = false
  }
}

// 格式化过期时间
const formatExpireTime = (timestamp: string) => {
  return formatFullDateTime(timestamp)
}
</script>
