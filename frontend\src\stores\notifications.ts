import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { FriendRequest } from '@/types'

export interface SystemNotification {
  id: string
  type: 'friend_request' | 'friend_accepted' | 'friend_declined' | 'system'
  title: string
  message: string
  data?: any
  timestamp: Date
  read: boolean
}

export const useNotificationsStore = defineStore('notifications', () => {
  // 状态
  const notifications = ref<SystemNotification[]>([])
  const friendRequests = ref<FriendRequest[]>([])

  // 计算属性
  const unreadCount = computed(() => {
    return notifications.value.filter(n => !n.read).length
  })

  const unreadFriendRequestsCount = computed(() => {
    return friendRequests.value.filter(req => req.status === 'pending').length
  })

  const totalUnreadCount = computed(() => {
    return unreadCount.value + unreadFriendRequestsCount.value
  })

  // 方法
  const addNotification = (notification: Omit<SystemNotification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: SystemNotification = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      read: false
    }
    notifications.value.unshift(newNotification)
    
    // 限制通知数量，最多保留50条
    if (notifications.value.length > 50) {
      notifications.value = notifications.value.slice(0, 50)
    }
  }

  const markAsRead = (notificationId: string) => {
    const notification = notifications.value.find(n => n.id === notificationId)
    if (notification) {
      notification.read = true
    }
  }

  const markAllAsRead = () => {
    notifications.value.forEach(n => n.read = true)
  }

  const removeNotification = (notificationId: string) => {
    const index = notifications.value.findIndex(n => n.id === notificationId)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  // 好友请求相关
  const addFriendRequest = (request: FriendRequest) => {
    console.log('🏪 STORE: addFriendRequest called with:', request)
    console.log('🏪 STORE: Current friendRequests before:', friendRequests.value.length)
    console.log('🏪 STORE: Current totalUnreadCount before:', totalUnreadCount.value)

    // 检查是否已存在
    const existingIndex = friendRequests.value.findIndex(r => r.id === request.id)
    if (existingIndex > -1) {
      console.log('🏪 STORE: Updating existing friend request at index:', existingIndex)
      friendRequests.value[existingIndex] = request
    } else {
      console.log('🏪 STORE: Adding new friend request')
      friendRequests.value.unshift(request)
    }

    console.log('🏪 STORE: Current friendRequests after:', friendRequests.value.length)
    console.log('🏪 STORE: Current totalUnreadCount after:', totalUnreadCount.value)
    console.log('🏪 STORE: All friend requests:', friendRequests.value.map(r => ({ id: r.id, sender: r.sender_username, status: r.status })))

    // 强制触发响应式更新（虽然通常不需要，但用于调试）
    console.log('🏪 STORE: Triggering reactivity check...')

    // 不再添加系统消息通知，只保留好友请求通知
    // 好友请求本身就会在通知图标中显示，无需重复的系统消息
  }

  const updateFriendRequest = (request: FriendRequest) => {
    const index = friendRequests.value.findIndex(r => r.id === request.id)
    if (index > -1) {
      friendRequests.value[index] = request
    }

    // 添加状态变更通知
    if (request.status === 'accepted') {
      addNotification({
        type: 'friend_accepted',
        title: '好友请求已接受',
        message: `${request.sender_username} 接受了你的好友请求`,
        data: request
      })
    } else if (request.status === 'declined') {
      addNotification({
        type: 'friend_declined',
        title: '好友请求被拒绝',
        message: `${request.sender_username} 拒绝了你的好友请求`,
        data: request
      })
    }
  }

  const removeFriendRequest = (requestId: number) => {
    const index = friendRequests.value.findIndex(r => r.id === requestId)
    if (index > -1) {
      friendRequests.value.splice(index, 1)
    }
  }

  const clearFriendRequests = () => {
    friendRequests.value = []
  }

  return {
    // 状态
    notifications,
    friendRequests,
    
    // 计算属性
    unreadCount,
    unreadFriendRequestsCount,
    totalUnreadCount,
    
    // 方法
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,
    addFriendRequest,
    updateFriendRequest,
    removeFriendRequest,
    clearFriendRequests
  }
})
