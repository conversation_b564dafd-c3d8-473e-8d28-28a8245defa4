<script setup lang="ts">
import { onMounted, watch, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chat'
import { useNotificationsStore } from '@/stores/notifications'
import { socketService } from '@/services/socket'
import { notificationService } from '@/services/notificationService'
import { audioService } from '@/services/audioService'
import { toastService } from '@/services/toastService'

const authStore = useAuthStore()
const chatStore = useChatStore()
const notificationsStore = useNotificationsStore()

// 原始页面标题
const originalTitle = ref('1v1 Chat')

// 显示好友请求通知
const showFriendRequestNotification = async (senderName: string) => {
  try {
    // 显示浏览器通知
    await notificationService.showFriendRequestNotification(senderName)

    // 显示Toast通知
    toastService.showFriendRequestToast(senderName)

    // 播放通知音效
    await audioService.playNotificationSound()
  } catch (error) {
    console.error('Failed to show friend request notification:', error)
  }
}

// 更新页面标题
const updatePageTitle = () => {
  const unreadCount = notificationsStore.totalUnreadCount
  if (unreadCount > 0) {
    document.title = `(${unreadCount}) ${originalTitle.value}`
  } else {
    document.title = originalTitle.value
  }
}

onMounted(async () => {
  // 初始化认证状态
  await authStore.initAuth()

  // 初始化页面标题
  originalTitle.value = document.title

  // 设置Socket.IO事件监听
  setupSocketListeners()

  // 如果用户已认证，确保Socket连接
  if (authStore.isAuthenticated) {
    await connectSocketForNotifications()
  }
})

// 监听通知数量变化，更新页面标题
watch(() => notificationsStore.totalUnreadCount, (newCount) => {
  updatePageTitle()
}, { immediate: true })

// 监听认证状态变化，确保登录后连接Socket
watch(() => authStore.isAuthenticated, async (isAuthenticated) => {
  if (isAuthenticated) {
    console.log('User authenticated, connecting socket for notifications...')
    await connectSocketForNotifications()
    // 重新设置监听器，确保在任何Socket重连后都有效
    setupSocketListeners()
  }
}, { immediate: false })

// 监听Socket连接状态，确保连接后重新设置监听器
watch(() => socketService.isConnected(), (isConnected) => {
  if (isConnected) {
    console.log('🔌 Socket connected, ensuring global listeners are set up...')
    // 延迟一点时间确保Socket完全就绪
    setTimeout(() => {
      setupSocketListeners()
    }, 100)
  }
}, { immediate: false })
watch(() => authStore.isAuthenticated, async (isAuthenticated) => {
  if (isAuthenticated) {
    // 用户登录后自动连接Socket.IO以接收通知
    console.log('User authenticated, connecting socket for notifications...')
    await connectSocketForNotifications()
  } else {
    // 用户登出时断开Socket连接
    console.log('User logged out, disconnecting socket...')
    globalListenersSetup = false // 重置监听器标志
    socketService.disconnect()
  }
})

// 连接Socket.IO用于接收通知
const connectSocketForNotifications = async () => {
  if (socketService.isConnected()) {
    console.log('🔌 Socket already connected for notifications')
    return
  }

  try {
    console.log('🔌 Connecting Socket.IO for notifications...')
    await socketService.connect()
    console.log('✅ Socket.IO connected successfully for notifications')
    console.log('🔌 Socket ID:', socketService.id)
    console.log('🔌 Socket connected:', socketService.isConnected())
  } catch (error) {
    console.error('❌ Failed to connect Socket.IO for notifications:', error)
  }
}

// 防止重复设置监听器的标志
let globalListenersSetup = false

const setupSocketListeners = () => {
  console.log('🔧 Setting up global socket listeners in App.vue')
  console.log('🔧 Socket connected:', socketService.isConnected())
  console.log('🔧 Socket instance:', socketService.socketInstance)
  console.log('🔧 Current user:', authStore.user)
  console.log('🔧 User authenticated:', authStore.isAuthenticated)
  console.log('🔧 Global listeners already setup:', globalListenersSetup)

  // 如果已经设置过全局监听器，就不要重复设置
  if (globalListenersSetup) {
    console.log('🔧 Global listeners already setup, skipping...')
    return
  }

  // 监听Socket连接事件
  socketService.on('connected', (data) => {
    console.log('✅ Socket connected event received:', data)
  })

  // 监听Socket错误
  socketService.on('error', (data) => {
    console.error('❌ Socket error event:', data)
  })

  // 监听好友请求相关事件 - 全局监听，确保在任何页面都能收到通知
  socketService.on('friend_request_received', (data) => {
    console.log('🔔 Global: Friend request received:', data)
    console.log('🔔 Current user ID:', authStore.user?.id)
    console.log('🔔 Socket connected:', socketService.isConnected())
    console.log('🔔 Socket ID:', socketService.socketInstance?.id)

    // 确保数据结构正确
    if (data && data.request) {
      console.log('📝 Adding friend request to notifications store...')
      console.log('📝 Request details:', data.request)
      notificationsStore.addFriendRequest(data.request)

      // 显示浏览器原生通知
      showFriendRequestNotification(data.request.sender_username)

      // 更新页面标题提示
      updatePageTitle()

      console.log('✅ Friend request added to notification store')
      console.log('✅ Total unread count:', notificationsStore.totalUnreadCount)
    } else {
      console.error('❌ Invalid friend request data:', data)
    }
  })

  socketService.on('friend_request_accepted', (data) => {
    console.log('✅ Global: Friend request accepted:', data)
    // 好友请求被接受后，从通知列表中移除该请求
    notificationsStore.removeFriendRequest(data.request.id)
  })

  socketService.on('friend_request_declined', (data) => {
    console.log('❌ Global: Friend request declined:', data)
    // 好友请求被拒绝后，从通知列表中移除该请求
    notificationsStore.removeFriendRequest(data.request.id)
  })

  // 标记全局监听器已设置
  globalListenersSetup = true
  console.log('🔧 Global socket listeners setup completed')
}


</script>

<template>
  <div id="app" class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <RouterView />
  </div>
</template>

<style scoped>
#app {
  font-family: 'Inter', system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
