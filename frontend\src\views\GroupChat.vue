<template>
  <div class="h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50 overflow-hidden">
    <!-- 顶部导航栏 -->
    <nav class="glass border-b border-white/30 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <RouterLink to="/" class="text-xl font-bold text-gradient">
            1v1 Chat
          </RouterLink>
          
          <div v-if="currentGroup" class="flex items-center space-x-2">
            <div class="w-2 h-2 rounded-full bg-green-400"></div>
            <span class="text-sm text-gray-600">
              群组: {{ currentGroup.name }}
            </span>
            <span class="text-xs text-gray-500">
              ({{ currentGroup.member_count }} 成员)
            </span>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-700">
            {{ authStore.user?.username }}
          </span>
          
          <RouterLink to="/friends" class="btn-secondary text-sm">
            好友管理
          </RouterLink>
          
          <RouterLink to="/chat" class="btn-secondary text-sm">
            私聊
          </RouterLink>
          
          <button 
            @click="handleLogout" 
            class="btn-secondary text-sm"
          >
            登出
          </button>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 群组列表侧边栏 -->
      <div class="w-80 glass border-r border-white/30 flex flex-col">
        <div class="p-4 border-b border-white/30">
          <h2 class="text-lg font-semibold text-gray-900">我的群组</h2>
        </div>
        
        <div class="flex-1 overflow-y-auto p-4 space-y-2">
          <div
            v-for="group in groups"
            :key="group.id"
            @click="selectGroup(group)"
            class="p-3 rounded-lg cursor-pointer transition-colors"
            :class="currentGroup?.id === group.id ? 'bg-primary-100 border border-primary-200' : 'bg-white/50 hover:bg-white/70'"
          >
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-900 truncate">{{ group.name }}</div>
                <div class="text-xs text-gray-500">{{ group.member_count }} 成员</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 聊天区域 -->
      <div class="flex-1 flex flex-col h-full">
        <div v-if="!currentGroup" class="flex-1 flex items-center justify-center">
          <div class="text-center">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">选择一个群组开始聊天</h3>
            <p class="text-gray-600">从左侧选择群组或创建新群组</p>
          </div>
        </div>

        <div v-else class="flex-1 flex flex-col h-full">
          <!-- 群组信息栏 -->
          <div class="flex-shrink-0 glass border-b border-white/30 p-4">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-medium text-gray-900">{{ currentGroup.name }}</h3>
                <p v-if="currentGroup.description" class="text-sm text-gray-500">{{ currentGroup.description }}</p>
              </div>
              <button
                @click="showGroupInfo = true"
                class="btn-secondary text-sm"
              >
                群组信息
              </button>
            </div>
          </div>

          <!-- 消息列表 -->
          <div class="flex-1 overflow-y-auto p-4 space-y-4 pb-4">
            <div
              v-for="message in groupMessages"
              :key="message.id"
              class="flex space-x-3"
            >
              <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span class="text-xs font-medium text-primary-600">
                  {{ message.sender_username.charAt(0).toUpperCase() }}
                </span>
              </div>
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-medium text-gray-900">{{ message.sender_username }}</span>
                  <span class="text-xs text-gray-500">{{ formatTime(message.timestamp) }}</span>
                </div>
                <div v-if="message.reply_to && message.parent_message" class="mt-1 p-2 bg-gray-100 rounded text-sm text-gray-600 border-l-2 border-gray-300">
                  <div class="text-xs text-gray-500">回复 {{ message.parent_message.sender_username }}:</div>
                  <div class="truncate">{{ message.parent_message.content }}</div>
                </div>
                <div class="mt-1 text-sm text-gray-800" :class="{ 'italic text-gray-500': message.is_recalled }">
                  {{ message.content }}
                </div>
              </div>
            </div>
          </div>

          <!-- 消息输入框 - 固定在底部 -->
          <div class="flex-shrink-0 glass border-t border-white/30 p-4">
            <form @submit.prevent="sendMessage" class="flex space-x-3">
              <input
                v-model="messageContent"
                type="text"
                placeholder="输入消息..."
                class="input-primary flex-1 text-black"
                :disabled="authStore.user?.muted"
              >
              <button
                type="submit"
                :disabled="!messageContent.trim() || authStore.user?.muted"
                class="btn-primary"
              >
                发送
              </button>
            </form>
            <div v-if="authStore.user?.muted" class="mt-2 text-sm text-red-600">
              您已被禁言，无法发送消息
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 群组信息模态框 -->
    <div v-if="showGroupInfo && currentGroup" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="glass rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[80vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">群组信息</h3>
          <button
            @click="showGroupInfo = false"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="space-y-6">
          <!-- 群组基本信息 -->
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-3">基本信息</h4>
            <div class="space-y-2">
              <div><span class="font-medium">名称：</span>{{ currentGroup.name }}</div>
              <div v-if="currentGroup.description"><span class="font-medium">描述：</span>{{ currentGroup.description }}</div>
              <div><span class="font-medium">成员数：</span>{{ currentGroup.member_count }} / {{ currentGroup.max_members }}</div>
              <div><span class="font-medium">创建时间：</span>{{ formatTime(currentGroup.created_at) }}</div>
            </div>
          </div>

          <!-- 成员列表 -->
          <div v-if="currentGroup.members">
            <h4 class="text-md font-medium text-gray-900 mb-3">成员列表</h4>
            <div class="space-y-2 max-h-40 overflow-y-auto">
              <div
                v-for="member in currentGroup.members"
                :key="member.user_id"
                class="flex items-center justify-between p-2 bg-white/50 rounded"
              >
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span class="text-xs font-medium text-primary-600">
                      {{ member.username.charAt(0).toUpperCase() }}
                    </span>
                  </div>
                  <span class="text-sm">{{ member.username }}</span>
                </div>
                <span :class="[
                  'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium',
                  member.role === 'owner' ? 'bg-purple-100 text-purple-800' :
                  member.role === 'admin' ? 'bg-blue-100 text-blue-800' :
                  'bg-gray-100 text-gray-800'
                ]">
                  {{ getRoleText(member.role) }}
                </span>
              </div>
            </div>
          </div>

          <div class="flex justify-end">
            <button
              @click="showGroupInfo = false"
              class="btn-primary"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { groupsAPI } from '@/services/api'
import { socketService } from '@/services/socket'
import { formatTime as formatTimeUtil } from '@/utils/time'
import type { Group, GroupMessage } from '@/types'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const groups = ref<Group[]>([])
const currentGroup = ref<Group | null>(null)
const groupMessages = ref<GroupMessage[]>([])
const messageContent = ref('')
const showGroupInfo = ref(false)

onMounted(async () => {
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  await loadGroups()
  
  // 如果URL中有群组ID，自动选择该群组
  const groupId = route.params.groupId
  if (groupId) {
    const group = groups.value.find(g => g.id === Number(groupId))
    if (group) {
      await selectGroup(group)
    }
  }

  // 设置Socket.IO事件监听
  setupSocketListeners()
})

onUnmounted(() => {
  // 离开当前群组房间
  if (currentGroup.value) {
    socketService.emit('leave_group', { group_id: currentGroup.value.id })
  }
})

const loadGroups = async () => {
  try {
    const response = await groupsAPI.getUserGroups({})
    groups.value = response.data.groups
  } catch (error) {
    console.error('Failed to load groups:', error)
  }
}

const selectGroup = async (group: Group) => {
  // 离开之前的群组房间
  if (currentGroup.value) {
    socketService.emit('leave_group', { group_id: currentGroup.value.id })
  }

  currentGroup.value = group
  
  // 加载群组详情和消息
  await Promise.all([
    loadGroupDetails(group.id),
    loadGroupMessages(group.id)
  ])
  
  // 加入新的群组房间
  socketService.emit('join_group', { group_id: group.id })
}

const loadGroupDetails = async (groupId: number) => {
  try {
    const response = await groupsAPI.getGroupDetails(groupId)
    currentGroup.value = response.data.group
  } catch (error) {
    console.error('Failed to load group details:', error)
  }
}

const loadGroupMessages = async (groupId: number) => {
  try {
    const response = await groupsAPI.getGroupMessages(groupId, {})
    groupMessages.value = response.data.messages
  } catch (error) {
    console.error('Failed to load group messages:', error)
  }
}

const sendMessage = async () => {
  if (!messageContent.value.trim() || !currentGroup.value) return

  try {
    const response = await groupsAPI.sendGroupMessage(currentGroup.value.id, {
      content: messageContent.value.trim()
    })
    
    messageContent.value = ''
    
    // 通过Socket.IO发送消息以实现实时更新
    socketService.emit('send_group_message', {
      group_id: currentGroup.value.id,
      content: response.data.group_message.content
    })
  } catch (error) {
    console.error('Failed to send message:', error)
  }
}

const setupSocketListeners = () => {
  // 监听新的群组消息
  socketService.on('new_group_message', (data: { message: GroupMessage }) => {
    if (currentGroup.value && data.message.group_id === currentGroup.value.id) {
      groupMessages.value.push(data.message)
    }
  })

  // 监听群组成员变化
  socketService.on('group_member_added', (data: any) => {
    if (currentGroup.value && data.group_id === currentGroup.value.id) {
      loadGroupDetails(currentGroup.value.id)
    }
  })

  socketService.on('group_member_removed', (data: any) => {
    if (currentGroup.value && data.group_id === currentGroup.value.id) {
      loadGroupDetails(currentGroup.value.id)
    }
  })
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('Failed to logout:', error)
  }
}

const getRoleText = (role: string) => {
  switch (role) {
    case 'owner': return '群主'
    case 'admin': return '管理员'
    case 'member': return '成员'
    default: return role
  }
}

const formatTime = (timeString: string) => {
  return formatTimeUtil(timeString)
}
</script>

<style scoped>
.input-primary {
  color: black !important;
}
</style>
