<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- 顶部导航栏 -->
    <nav class="glass border-b border-white/30 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <button @click="goBack" class="text-gray-600 hover:text-gray-800">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <h1 class="text-xl font-bold text-gradient">问卷回答详情</h1>
        </div>
        
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-700">
            {{ authStore.user?.username }}
          </span>
        </div>
      </div>
    </nav>

    <div class="max-w-4xl mx-auto px-6 py-8">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
        <p class="mt-4 text-gray-600">加载回答详情中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-600 mb-4">
          <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h2 class="text-xl font-semibold text-gray-900 mb-2">加载失败</h2>
        <p class="text-gray-600 mb-4">{{ error }}</p>
        <button @click="loadResponse" class="btn-primary">
          重试
        </button>
      </div>

      <!-- 回答详情 -->
      <div v-else-if="response" class="space-y-8">
        <!-- 问卷基本信息 -->
        <div class="card-glass">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">{{ response.questionnaire?.title }}</h2>
          <p v-if="response.questionnaire?.description" class="text-gray-600 mb-6">
            {{ response.questionnaire?.description }}
          </p>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="font-medium text-gray-700">回答者：</span>
              <span class="text-gray-900">{{ getRespondentName() }}</span>
            </div>
            <div>
              <span class="font-medium text-gray-700">提交时间：</span>
              <span class="text-gray-900">{{ formatDate(response.submitted_at) }}</span>
            </div>
          </div>
        </div>

        <!-- 回答内容 -->
        <div class="space-y-6">
          <div
            v-for="(page, pageIndex) in response.questionnaire?.pages"
            :key="page.id"
            class="card-glass"
          >
            <div v-if="page.title || page.description" class="border-b border-gray-200 pb-4 mb-6">
              <h3 v-if="page.title" class="text-xl font-semibold text-gray-900 mb-2">
                第 {{ pageIndex + 1 }} 页：{{ page.title }}
              </h3>
              <p v-if="page.description" class="text-gray-600">
                {{ page.description }}
              </p>
            </div>

            <div class="space-y-6">
              <div
                v-for="(question, questionIndex) in page.questions"
                :key="question.id"
                class="bg-gray-50 rounded-lg p-6"
              >
                <div class="flex items-start space-x-3 mb-4">
                  <span class="flex-shrink-0 w-6 h-6 bg-primary-600 text-white text-sm font-medium rounded-full flex items-center justify-center">
                    {{ questionIndex + 1 }}
                  </span>
                  <div class="flex-1">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">
                      {{ question.question_text }}
                      <span v-if="question.is_required" class="text-red-500 ml-1">*</span>
                    </h4>

                    <!-- 回答内容 -->
                    <div class="bg-white rounded-lg p-4 border border-gray-200">
                      <div v-if="getAnswerForQuestion(question.id)" class="space-y-3">
                        <!-- 隐藏状态提示 -->
                        <div v-if="getAnswerForQuestion(question.id)?.is_hidden && !getAnswerForQuestion(question.id)?.is_revealed" 
                             class="bg-red-50 border border-red-200 rounded-lg p-3">
                          <div class="flex items-center space-x-2 mb-2">
                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L12 12m-2.122-2.122L7.758 7.758M12 12l2.122-2.122m0 0L16.242 7.758M12 12l-2.122 2.122"></path>
                            </svg>
                            <span class="font-medium text-red-800">此答案已隐藏</span>
                          </div>
                          <p class="text-sm text-red-700 mb-3">回答者选择隐藏此题的答案</p>
                          <div class="flex space-x-2">
                            <button
                              v-if="canReveal()"
                              @click="revealAnswer(getAnswerForQuestion(question.id))"
                              class="btn-primary text-sm"
                            >
                              揭秘答案
                            </button>
                            <button
                              v-else
                              @click="requestReveal(getAnswerForQuestion(question.id))"
                              class="btn-secondary text-sm"
                            >
                              申请揭秘
                            </button>
                          </div>
                        </div>

                        <!-- 已揭秘的隐藏答案 -->
                        <div v-else-if="getAnswerForQuestion(question.id)?.is_hidden && getAnswerForQuestion(question.id)?.is_revealed"
                             class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-3">
                          <div class="flex items-center space-x-2 mb-2">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            <span class="font-medium text-yellow-800">已揭秘答案</span>
                          </div>
                          <p class="text-sm text-yellow-700">
                            揭秘时间：{{ formatDate(getAnswerForQuestion(question.id)?.revealed_at) }}
                          </p>
                        </div>

                        <!-- 显示答案内容 -->
                        <div v-if="!getAnswerForQuestion(question.id)?.is_hidden || getAnswerForQuestion(question.id)?.is_revealed">
                          <!-- 选择题答案 -->
                          <div v-if="question.question_type !== 'text_input'">
                            <div class="space-y-2">
                              <div
                                v-for="option in getAnswerForQuestion(question.id)?.selected_options"
                                :key="option.id"
                                class="flex items-center space-x-2"
                              >
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-900">{{ option.option_text }}</span>
                              </div>
                            </div>
                          </div>

                          <!-- 文本答案 -->
                          <div v-else>
                            <p class="text-gray-900 whitespace-pre-wrap">{{ getAnswerForQuestion(question.id)?.text_answer }}</p>
                          </div>
                        </div>
                      </div>

                      <!-- 未回答 -->
                      <div v-else class="text-gray-500 italic">
                        未回答
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-center pt-6">
          <button @click="goBack" class="btn-secondary">
            返回聊天
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { questionnaireAPI } from '@/services/questionnaire'
import { formatFullDateTime } from '@/utils/time'
import type { QuestionnaireResponse, Answer } from '@/types'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const loading = ref(false)
const error = ref('')
const response = ref<QuestionnaireResponse | null>(null)

// 路由参数
const responseId = computed(() => parseInt(route.params.responseId as string))

// 获取回答者姓名
const getRespondentName = () => {
  // 这里需要根据实际情况获取回答者信息
  return '用户' // 临时显示
}

// 获取指定题目的回答
const getAnswerForQuestion = (questionId: number): Answer | undefined => {
  return response.value?.answers.find(answer => answer.question_id === questionId)
}

// 检查是否可以揭秘（回答者可以揭秘自己的答案）
const canReveal = () => {
  return authStore.user?.id === response.value?.respondent_id
}

// 格式化日期
const formatDate = (dateString?: string) => {
  if (!dateString) return '未知'
  return formatFullDateTime(dateString)
}

// 加载回答详情
const loadResponse = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const result = await questionnaireAPI.getQuestionnaireResponse(responseId.value)
    response.value = result.response
  } catch (err: any) {
    error.value = err.response?.data?.error || '加载回答详情失败'
  } finally {
    loading.value = false
  }
}

// 揭秘答案
const revealAnswer = async (answer: Answer) => {
  try {
    await questionnaireAPI.revealAnswer(answer.id)
    answer.is_revealed = true
    answer.revealed_at = new Date().toISOString()
    alert('答案已揭秘')
  } catch (err: any) {
    alert(err.response?.data?.error || '揭秘失败')
  }
}

// 申请揭秘
const requestReveal = async (answer: Answer) => {
  try {
    await questionnaireAPI.requestRevealAnswer(answer.id)
    alert('揭秘申请已提交')
  } catch (err: any) {
    alert(err.response?.data?.error || '申请失败')
  }
}

// 返回
const goBack = () => {
  router.back()
}

onMounted(() => {
  loadResponse()
})
</script>

<style scoped>
.card-glass {
  @apply bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/30 p-6;
}

.glass {
  @apply bg-white/80 backdrop-blur-sm;
}

.text-gradient {
  @apply bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent;
}

.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}
</style>
