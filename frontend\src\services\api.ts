import axios, { type AxiosResponse } from 'axios'
import type {
  User,
  Message,
  ChatLink,
  InviteCode,
  Conversation,
  ApiResponse,
  Pagination,
  LoginForm,
  RegisterForm,
  AdminStats,
  FriendRequest,
  FriendRequestForm,
  BlockedUser,
  Group,
  GroupMembership,
  GroupMessage,
  GroupCreateForm,
  GroupUpdateForm,
  GroupMessageForm
} from '@/types'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  withCredentials: true, // 支持cookie
  timeout: 10000,
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 全局API调用跟踪器
let apiTracker: ((method: string, url: string, status: string) => void) | null = null

export const setApiTracker = (tracker: (method: string, url: string, status: string) => void) => {
  apiTracker = tracker
}

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    if (apiTracker) {
      apiTracker(config.method?.toUpperCase() || 'UNKNOWN', config.url || '', 'sending')
    }
    return config
  },
  (error) => {
    if (apiTracker) {
      apiTracker('UNKNOWN', 'UNKNOWN', 'request_error')
    }
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    if (apiTracker) {
      apiTracker(
        response.config.method?.toUpperCase() || 'UNKNOWN',
        response.config.url || '',
        `${response.status}`
      )
    }
    return response
  },
  async (error) => {
    if (apiTracker) {
      apiTracker(
        error.config?.method?.toUpperCase() || 'UNKNOWN',
        error.config?.url || '',
        `${error.response?.status || 'network_error'}`
      )
    }

    // 统一错误处理
    if (error.response?.status === 401) {
      console.warn('Unauthorized access')

      // 如果是聊天链接相关的401错误，且不是匿名用户创建请求本身，尝试创建匿名用户
      const isAnonymousUserRequest = error.config?.url?.includes('/auth/anonymous')
      const isChatLinkRequest = error.config?.url?.includes('/chat/links/') && error.config?.method === 'post'

      if (isChatLinkRequest && !isAnonymousUserRequest) {
        console.log('Chat link request failed with 401, this should have been handled by the component')
      }
    }
    return Promise.reject(error)
  }
)

// 认证相关API
export const authAPI = {
  // 用户注册
  register: (data: RegisterForm): Promise<AxiosResponse<ApiResponse<{ user: User }>>> =>
    api.post('/auth/register', data),
  
  // 用户登录
  login: (data: LoginForm): Promise<AxiosResponse<ApiResponse<{ user: User }>>> =>
    api.post('/auth/login', data),
  
  // 用户登出
  logout: (): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/auth/logout'),
  
  // 获取当前用户
  getCurrentUser: (): Promise<AxiosResponse<ApiResponse<{ user: User }>>> =>
    api.get('/auth/me'),
  
  // 创建匿名用户
  createAnonymousUser: (): Promise<AxiosResponse<ApiResponse<{ user: User }>>> =>
    api.post('/auth/anonymous'),
  
  // 检查用户名可用性
  checkUsername: (username: string): Promise<AxiosResponse<{ available: boolean; username: string; error?: string }>> =>
    api.post('/auth/check-username', { username }),
  
  // 验证邀请码
  validateInviteCode: (code: string): Promise<AxiosResponse<{ valid: boolean; code: string; invite?: InviteCode; error?: string }>> =>
    api.post('/auth/validate-invite', { code }),

  // 使用邀请码开始对话
  useInviteCode: (code: string): Promise<AxiosResponse<ApiResponse<{ creator: User; invite: InviteCode }>>> =>
    api.post('/auth/use-invite', { code }),

  // 上传头像
  uploadAvatar: (file: File): Promise<AxiosResponse<{ message: string; avatar_url: string; user: User }>> => {
    const formData = new FormData()
    formData.append('avatar', file)
    return api.post('/auth/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
}

// 聊天相关API
export const chatAPI = {
  // 获取消息列表
  getMessages: (userId: number, page = 1, perPage = 50): Promise<AxiosResponse<{
    messages: Message[]
    pagination: Pagination
  }>> =>
    api.get('/chat/messages', { params: { user_id: userId, page, per_page: perPage } }),
  
  // 发送消息
  sendMessage: (data: {
    content: string
    recipient_id: number
    reply_to?: number
  }): Promise<AxiosResponse<ApiResponse<{ data: Message }>>> =>
    api.post('/chat/messages', data),
  
  // 撤回消息
  recallMessage: (messageId: number): Promise<AxiosResponse<ApiResponse<{ data: Message }>>> =>
    api.patch(`/chat/messages/${messageId}/recall`),

  // 上传图片
  uploadImage: (file: File): Promise<AxiosResponse<{
    message: string;
    image_url: string;
    thumbnail_url: string;
    filename: string
  }>> => {
    const formData = new FormData()
    formData.append('image', file)
    return api.post('/chat/upload-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  // 获取会话列表
  getConversations: (): Promise<AxiosResponse<{ conversations: Conversation[] }>> =>
    api.get('/chat/conversations'),

  // 删除对话
  deleteConversation: (userId: number): Promise<AxiosResponse<ApiResponse<{ deleted_messages_count: number }>>> =>
    api.delete(`/chat/conversations/${userId}`),
  
  // 创建聊天链接
  createChatLink: (data: {
    expire_hours?: number
    single_use?: boolean
    note?: string
  }): Promise<AxiosResponse<ApiResponse<{ link: ChatLink; url: string }>>> =>
    api.post('/chat/links', data),
  
  // 验证聊天链接
  validateChatLink: (code: string): Promise<AxiosResponse<{ valid: boolean; link: ChatLink; error?: string }>> =>
    api.get(`/chat/links/${code}`),
  
  // 使用聊天链接
  useChatLink: (code: string): Promise<AxiosResponse<ApiResponse<{ creator: User }>>> =>
    api.post(`/chat/links/${code}/use`),
  
  // 搜索用户
  searchUsers: (query: string): Promise<AxiosResponse<{ users: User[] }>> =>
    api.get('/chat/users/search', { params: { q: query } }),

  // 通过ID获取用户
  getUserById: (userId: number): Promise<AxiosResponse<{ user: User }>> =>
    api.get(`/chat/users/${userId}`),

  // 标记消息为已读
  markMessagesAsRead: (otherUserId: number): Promise<AxiosResponse<ApiResponse<{ marked_count: number }>>> =>
    api.post('/chat/messages/mark-read', { other_user_id: otherUserId }),
}

// 管理员相关API
export const adminAPI = {
  // 获取统计信息
  getStats: (): Promise<AxiosResponse<AdminStats>> =>
    api.get('/admin/stats'),
  
  // 获取用户列表
  getUsers: (params: {
    page?: number
    per_page?: number
    search?: string
    type?: 'all' | 'anonymous' | 'registered' | 'banned'
  }): Promise<AxiosResponse<{
    users: User[]
    pagination: Pagination
  }>> =>
    api.get('/admin/users', { params }),
  
  // 封禁用户
  banUser: (userId: number): Promise<AxiosResponse<ApiResponse<{ user: User }>>> =>
    api.put(`/admin/users/${userId}/ban`),
  
  // 解封用户
  unbanUser: (userId: number): Promise<AxiosResponse<ApiResponse<{ user: User }>>> =>
    api.put(`/admin/users/${userId}/unban`),
  
  // 禁言用户
  muteUser: (userId: number): Promise<AxiosResponse<ApiResponse<{ user: User }>>> =>
    api.put(`/admin/users/${userId}/mute`),
  
  // 解除禁言
  unmuteUser: (userId: number): Promise<AxiosResponse<ApiResponse<{ user: User }>>> =>
    api.put(`/admin/users/${userId}/unmute`),
  
  // 生成邀请码
  generateInvites: (data: { count: number; note?: string }): Promise<AxiosResponse<ApiResponse<{ invites: InviteCode[] }>>> =>
    api.post('/admin/invites/generate', data),
  
  // 获取邀请码列表
  getInvites: (params: {
    page?: number
    per_page?: number
    status?: 'all' | 'used' | 'unused'
  }): Promise<AxiosResponse<{
    invites: InviteCode[]
    pagination: Pagination
  }>> =>
    api.get('/admin/invites', { params }),
  
  // 导出邀请码
  exportInvites: (status: 'all' | 'used' | 'unused' = 'unused'): Promise<AxiosResponse<Blob>> =>
    api.get('/admin/invites/export', { 
      params: { status },
      responseType: 'blob'
    }),
  
  // 获取消息列表（管理员查看）
  getMessages: (params: {
    page?: number
    per_page?: number
    user_id?: number
  }): Promise<AxiosResponse<{
    messages: Message[]
    pagination: Pagination
  }>> =>
    api.get('/admin/messages', { params }),
  
  // 获取聊天链接列表
  getChatLinks: (params: {
    page?: number
    per_page?: number
  }): Promise<AxiosResponse<{
    links: ChatLink[]
    pagination: Pagination
  }>> =>
    api.get('/admin/links', { params }),

  // 获取用户详情
  getUserDetail: (userId: number): Promise<AxiosResponse<{ user: User & { statistics: any; recent_messages: any[] } }>> =>
    api.get(`/admin/users/${userId}`),

  // 批量用户操作
  batchUserOperations: (data: {
    user_ids: number[]
    operation: 'ban' | 'unban' | 'mute' | 'unmute' | 'delete'
  }): Promise<AxiosResponse<ApiResponse<{ affected_users: User[] }>>> =>
    api.post('/admin/users/batch', data),

  // 获取用户活动统计
  getUserActivity: (userId: number, days?: number): Promise<AxiosResponse<{
    user_id: number
    period_days: number
    daily_activity: Array<{ date: string; message_count: number }>
    chat_partners: Array<{ user: User; message_count: number }>
    hourly_activity: Array<{ hour: number; message_count: number }>
  }>> =>
    api.get(`/admin/users/${userId}/activity`, { params: { days } }),

  // 导出聊天记录
  exportMessages: (params: {
    format?: 'csv' | 'json'
    user_id?: number
    sender_id?: number
    recipient_id?: number
    start_date?: string
    end_date?: string
    limit?: number
  }): Promise<AxiosResponse<Blob>> =>
    api.get('/admin/messages/export', {
      params,
      responseType: 'blob'
    }),

  // 获取所有会话列表
  getAllConversations: (params: {
    page?: number
    per_page?: number
  }): Promise<AxiosResponse<{
    conversations: Array<{
      participants: User[]
      message_count: number
      latest_message: Message
      last_activity: string
    }>
    pagination: Pagination
  }>> =>
    api.get('/admin/conversations', { params }),

  // 更新用户群组创建权限
  updateGroupPermission: (userId: number, canCreateGroups: boolean): Promise<AxiosResponse<ApiResponse<{ user: User }>>> =>
    api.put(`/admin/users/${userId}/group-permission`, { can_create_groups: canCreateGroups }),

  // 获取所有群组
  getAllGroups: (params: {
    page?: number
    per_page?: number
    search?: string
  }): Promise<AxiosResponse<{
    groups: Group[]
    pagination: Pagination
  }>> =>
    api.get('/admin/groups', { params }),

  // 获取群组详情（管理员视图）
  getGroupAdminDetails: (groupId: number): Promise<AxiosResponse<{ group: Group }>> =>
    api.get(`/admin/groups/${groupId}`),

  // 管理员删除群组
  adminDeleteGroup: (groupId: number): Promise<AxiosResponse<ApiResponse>> =>
    api.delete(`/admin/groups/${groupId}`),

  // 获取所有好友请求
  getAllFriendRequests: (params: {
    page?: number
    per_page?: number
    status?: 'all' | 'pending' | 'accepted' | 'declined'
  }): Promise<AxiosResponse<{
    friend_requests: FriendRequest[]
    pagination: Pagination
  }>> =>
    api.get('/admin/friend-requests', { params }),

  // 获取所有屏蔽关系
  getAllBlockedRelationships: (params: {
    page?: number
    per_page?: number
  }): Promise<AxiosResponse<{
    blocked_relationships: BlockedUser[]
    pagination: Pagination
  }>> =>
    api.get('/admin/blocked-users', { params }),
}

// 好友管理相关API
export const friendsAPI = {
  // 搜索用户
  searchUsers: (params: {
    q: string
    page?: number
    per_page?: number
  }): Promise<AxiosResponse<{
    users: User[]
    pagination: Pagination
  }>> =>
    api.get('/friends/search', { params }),

  // 发送好友请求
  sendFriendRequest: (data: FriendRequestForm): Promise<AxiosResponse<ApiResponse<{ request: FriendRequest }>>> =>
    api.post('/friends/requests', data),

  // 获取好友请求列表
  getFriendRequests: (params: {
    type?: 'received' | 'sent' | 'all'
    page?: number
    per_page?: number
  }): Promise<AxiosResponse<{
    requests: FriendRequest[]
    pagination: Pagination
  }>> =>
    api.get('/friends/requests', { params }),

  // 接受好友请求
  acceptFriendRequest: (requestId: number): Promise<AxiosResponse<ApiResponse<{ request: FriendRequest }>>> =>
    api.put(`/friends/requests/${requestId}/accept`),

  // 拒绝好友请求
  declineFriendRequest: (requestId: number): Promise<AxiosResponse<ApiResponse<{ request: FriendRequest }>>> =>
    api.put(`/friends/requests/${requestId}/decline`),

  // 标记好友请求为已读
  markFriendRequestAsRead: (requestId: number): Promise<AxiosResponse<ApiResponse<{ request: FriendRequest }>>> =>
    api.put(`/friends/requests/${requestId}/mark-read`),

  // 标记所有好友请求为已读
  markAllFriendRequestsAsRead: (): Promise<AxiosResponse<ApiResponse<{ count: number }>>> =>
    api.put('/friends/requests/mark-all-read'),

  // 获取好友列表
  getFriends: (params: {
    page?: number
    per_page?: number
  }): Promise<AxiosResponse<{
    friends: User[]
    pagination: Pagination
  }>> =>
    api.get('/friends/list', { params }),

  // 移除好友
  removeFriend: (friendId: number): Promise<AxiosResponse<ApiResponse>> =>
    api.delete(`/friends/${friendId}`),

  // 屏蔽用户
  blockUser: (userId: number): Promise<AxiosResponse<ApiResponse<{ blocked_user: BlockedUser }>>> =>
    api.post('/friends/block', { user_id: userId }),

  // 解除屏蔽
  unblockUser: (blockedUserId: number): Promise<AxiosResponse<ApiResponse>> =>
    api.delete(`/friends/block/${blockedUserId}`),

  // 获取被屏蔽的用户列表
  getBlockedUsers: (params: {
    page?: number
    per_page?: number
  }): Promise<AxiosResponse<{
    blocked_users: BlockedUser[]
    pagination: Pagination
  }>> =>
    api.get('/friends/blocked', { params }),

  // 获取被限制的用户列表
  getRestrictedUsers: (params: {
    page?: number
    per_page?: number
  }): Promise<AxiosResponse<{
    restricted_users: Array<{
      user: User
      reject_count: number
      last_rejected_at: string | null
      record_id: number
    }>
    pagination: Pagination
  }>> =>
    api.get('/friends/restricted', { params }),

  // 恢复用户申请权限
  restoreUserPermission: (userId: number): Promise<AxiosResponse<ApiResponse<{
    user: User
    reject_count: number
  }>>> =>
    api.put(`/friends/restricted/${userId}/restore`),

  // 更新隐私设置
  updatePrivacySetting: (settings: {
    privacy_setting?: 'public' | 'friends_only' | 'private'
    allow_stranger_messages?: boolean
  }): Promise<AxiosResponse<ApiResponse<{ user: User }>>> =>
    api.put('/friends/privacy', settings),
}

// 群组管理相关API
export const groupsAPI = {
  // 创建群组
  createGroup: (data: GroupCreateForm): Promise<AxiosResponse<ApiResponse<{ group: Group }>>> =>
    api.post('/groups', data),

  // 获取用户的群组列表
  getUserGroups: (params: {
    page?: number
    per_page?: number
  }): Promise<AxiosResponse<{
    groups: Group[]
    pagination: Pagination
  }>> =>
    api.get('/groups', { params }),

  // 获取群组详情
  getGroupDetails: (groupId: number): Promise<AxiosResponse<{ group: Group }>> =>
    api.get(`/groups/${groupId}`),

  // 加入群组
  joinGroup: (groupId: number): Promise<AxiosResponse<ApiResponse<{ membership: GroupMembership }>>> =>
    api.post(`/groups/${groupId}/join`),

  // 离开群组
  leaveGroup: (groupId: number): Promise<AxiosResponse<ApiResponse>> =>
    api.post(`/groups/${groupId}/leave`),

  // 添加成员到群组
  addMember: (groupId: number, userId: number): Promise<AxiosResponse<ApiResponse<{ membership: GroupMembership }>>> =>
    api.post(`/groups/${groupId}/members/${userId}`),

  // 从群组移除成员
  removeMember: (groupId: number, userId: number): Promise<AxiosResponse<ApiResponse>> =>
    api.delete(`/groups/${groupId}/members/${userId}`),

  // 更新成员角色
  updateMemberRole: (groupId: number, userId: number, role: 'member' | 'admin'): Promise<AxiosResponse<ApiResponse<{ membership: GroupMembership }>>> =>
    api.put(`/groups/${groupId}/members/${userId}/role`, { role }),

  // 获取群组消息
  getGroupMessages: (groupId: number, params: {
    page?: number
    per_page?: number
  }): Promise<AxiosResponse<{
    messages: GroupMessage[]
    pagination: Pagination
  }>> =>
    api.get(`/groups/${groupId}/messages`, { params }),

  // 发送群组消息
  sendGroupMessage: (groupId: number, data: GroupMessageForm): Promise<AxiosResponse<ApiResponse<{ group_message: GroupMessage }>>> =>
    api.post(`/groups/${groupId}/messages`, data),

  // 更新群组信息
  updateGroup: (groupId: number, data: GroupUpdateForm): Promise<AxiosResponse<ApiResponse<{ group: Group }>>> =>
    api.put(`/groups/${groupId}`, data),

  // 删除群组
  deleteGroup: (groupId: number): Promise<AxiosResponse<ApiResponse>> =>
    api.delete(`/groups/${groupId}`),
}

export default api
