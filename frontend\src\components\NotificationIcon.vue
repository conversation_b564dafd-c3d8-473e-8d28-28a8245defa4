<template>
  <div class="relative">
    <!-- 通知图标按钮 -->
    <button
      ref="notificationButton"
      @click="toggleDropdown"
      class="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-white/50 rounded-lg transition-colors"
      :class="{ 'text-primary-600': showDropdown }"
    >
      <!-- 铃铛图标 -->
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9">
        </path>
      </svg>
      
      <!-- 未读数量徽章 -->
      <span
        v-if="notificationsStore.totalUnreadCount > 0"
        class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium animate-pulse shadow-lg"
        :class="{ 'animate-bounce': hasNewNotification }"
      >
        {{ notificationsStore.totalUnreadCount > 99 ? '99+' : notificationsStore.totalUnreadCount }}
      </span>

      <!-- 新通知指示器 -->
      <div
        v-if="hasNewNotification"
        class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping"
      ></div>
    </button>

    <!-- 使用 Teleport 将通知下拉菜单移动到 body 顶层 -->
    <Teleport to="body">
      <div
        v-if="showDropdown"
        ref="dropdownRef"
        class="fixed w-80 bg-white rounded-lg shadow-xl border border-gray-200 notification-dropdown"
        :class="[
          dropdownPosition === 'top' ? 'mb-2' : 'mt-2'
        ]"
        :style="dropdownStyle"
        @click.stop
      >
      <!-- 头部 -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">通知</h3>
        <div class="flex items-center space-x-2">
          <button
            v-if="notificationsStore.unreadCount > 0"
            @click="markAllAsRead"
            class="text-sm text-primary-600 hover:text-primary-700"
          >
            全部已读
          </button>
          <button
            @click="clearAll"
            class="text-sm text-gray-500 hover:text-gray-700"
          >
            清空
          </button>
        </div>
      </div>

      <!-- 通知列表 -->
      <div class="max-h-80 overflow-y-auto">
        <!-- 好友请求 -->
        <div v-if="notificationsStore.friendRequests.length > 0" class="p-2">
          <h4 class="text-sm font-medium text-gray-700 px-2 py-1">好友请求</h4>
          <div
            v-for="request in pendingFriendRequests"
            :key="request.id"
            class="p-3 hover:bg-gray-50 rounded-lg cursor-pointer"
            @click="handleFriendRequest(request)"
          >
            <div class="flex items-start space-x-3">
              <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                <span class="text-xs font-medium text-primary-600">
                  {{ request.sender_username.charAt(0).toUpperCase() }}
                </span>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900">
                  {{ request.sender_username }}
                </p>
                <p class="text-xs text-gray-500">
                  {{ request.message || '想要添加你为好友' }}
                </p>
                <p class="text-xs text-gray-400 mt-1">
                  {{ formatTime(request.created_at) }}
                </p>
              </div>
              <div class="w-2 h-2 bg-red-500 rounded-full"></div>
            </div>
          </div>
        </div>

        <!-- 系统通知 -->
        <div v-if="notificationsStore.notifications.length > 0" class="p-2">
          <h4 class="text-sm font-medium text-gray-700 px-2 py-1">系统消息</h4>
          <div
            v-for="notification in notificationsStore.notifications"
            :key="notification.id"
            class="p-3 hover:bg-gray-50 rounded-lg cursor-pointer"
            :class="{ 'bg-blue-50': !notification.read }"
            @click="handleNotificationClick(notification)"
          >
            <div class="flex items-start space-x-3">
              <div class="w-8 h-8 rounded-full flex items-center justify-center"
                   :class="getNotificationIconClass(notification.type)">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path v-if="notification.type === 'friend_request'" 
                        d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z">
                  </path>
                  <path v-else-if="notification.type === 'friend_accepted'"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z">
                  </path>
                  <path v-else-if="notification.type === 'friend_declined'"
                        d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z">
                  </path>
                  <path v-else
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                  </path>
                </svg>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900">
                  {{ notification.title }}
                </p>
                <p class="text-xs text-gray-600">
                  {{ notification.message }}
                </p>
                <p class="text-xs text-gray-400 mt-1">
                  {{ formatTime(notification.timestamp) }}
                </p>
              </div>
              <div v-if="!notification.read" class="w-2 h-2 bg-blue-500 rounded-full"></div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="notificationsStore.notifications.length === 0 && notificationsStore.friendRequests.length === 0" 
             class="p-8 text-center">
          <svg class="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9">
            </path>
          </svg>
          <p class="text-gray-500 text-sm">暂无通知</p>
        </div>
      </div>
    </div>
    </Teleport>

    <!-- 使用 Teleport 将背景遮罩也移动到 body 顶层 -->
    <Teleport to="body">
      <div
        v-if="showDropdown"
        class="fixed inset-0 notification-backdrop"
        @click="showDropdown = false"
      ></div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useNotificationsStore } from '@/stores/notifications'
import { useRouter } from 'vue-router'
import { formatTime } from '@/utils/time'
import type { SystemNotification, FriendRequest } from '@/stores/notifications'

const notificationsStore = useNotificationsStore()
const router = useRouter()
const showDropdown = ref(false)
const dropdownPosition = ref<'top' | 'bottom'>('top')
const notificationButton = ref<HTMLElement>()
const dropdownRef = ref<HTMLElement>()
const dropdownStyle = ref<Record<string, string>>({})
const hasNewNotification = ref(false)

const pendingFriendRequests = computed(() => {
  return notificationsStore.friendRequests.filter(req => req.status === 'pending')
})

// 添加调试信息
watch(() => notificationsStore.totalUnreadCount, (newCount, oldCount) => {
  console.log('NotificationIcon: Total unread count changed to:', newCount)

  // 如果有新的未读通知，触发动画
  if (newCount > oldCount && newCount > 0) {
    hasNewNotification.value = true
    // 3秒后停止动画
    setTimeout(() => {
      hasNewNotification.value = false
    }, 3000)
  }
})

watch(() => notificationsStore.friendRequests.length, (newLength) => {
  console.log('NotificationIcon: Friend requests count changed to:', newLength)
})

const toggleDropdown = async () => {
  showDropdown.value = !showDropdown.value

  if (showDropdown.value) {
    // 停止新通知动画
    hasNewNotification.value = false
    await nextTick()
    calculateDropdownPosition()
  }
}

const calculateDropdownPosition = () => {
  if (!notificationButton.value) return

  const buttonRect = notificationButton.value.getBoundingClientRect()
  const windowHeight = window.innerHeight
  const windowWidth = window.innerWidth
  const dropdownHeight = 320 // 估算的下拉菜单高度（max-h-80 = 320px）
  const dropdownWidth = 320 // w-80 = 320px

  // 计算水平位置（右对齐）
  let left = buttonRect.right - dropdownWidth
  if (left < 8) { // 最小边距8px
    left = 8
  }
  if (left + dropdownWidth > windowWidth - 8) {
    left = windowWidth - dropdownWidth - 8
  }

  // 计算垂直位置
  let top: number
  if (buttonRect.bottom + dropdownHeight > windowHeight - 8) {
    // 向上显示
    dropdownPosition.value = 'top'
    top = buttonRect.top - dropdownHeight - 8
    if (top < 8) {
      top = 8
    }
  } else {
    // 向下显示
    dropdownPosition.value = 'bottom'
    top = buttonRect.bottom + 8
  }

  // 设置样式
  dropdownStyle.value = {
    left: `${left}px`,
    top: `${top}px`
  }
}

const markAllAsRead = () => {
  notificationsStore.markAllAsRead()
}

const clearAll = () => {
  notificationsStore.clearAllNotifications()
  notificationsStore.clearFriendRequests()
}

const handleNotificationClick = (notification: SystemNotification) => {
  // 点击通知后删除该通知（而不是仅标记为已读）
  notificationsStore.removeNotification(notification.id)

  // 根据通知类型执行相应操作
  if (notification.type === 'friend_request' && notification.data) {
    router.push('/friends')
  }

  showDropdown.value = false
}

const handleFriendRequest = (request: FriendRequest) => {
  // 标记该好友请求为已读（从通知列表中移除）
  notificationsStore.removeFriendRequest(request.id)

  // 跳转到好友页面
  router.push('/friends')
  showDropdown.value = false
}

const getNotificationIconClass = (type: string) => {
  switch (type) {
    case 'friend_request':
      return 'bg-blue-100 text-blue-600'
    case 'friend_accepted':
      return 'bg-green-100 text-green-600'
    case 'friend_declined':
      return 'bg-red-100 text-red-600'
    default:
      return 'bg-gray-100 text-gray-600'
  }
}



// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && showDropdown.value) {
    showDropdown.value = false
  }
}

// 窗口大小变化处理
const handleResize = () => {
  if (showDropdown.value) {
    calculateDropdownPosition()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 强制设置通知下拉菜单的最高层级，确保不被任何元素遮挡 */
.notification-dropdown {
  z-index: 2147483647 !important; /* 使用最大的32位整数值 */
  /* 确保创建新的层级上下文 */
  position: fixed !important;
  /* 防止被backdrop-blur等效果影响 */
  isolation: isolate;
  /* 确保在所有可能的容器之上 */
  pointer-events: auto !important;
}

/* 背景遮罩层级 */
.notification-backdrop {
  z-index: 2147483646 !important; /* 比下拉菜单低1 */
  position: fixed !important;
  pointer-events: auto !important;
}
</style>
