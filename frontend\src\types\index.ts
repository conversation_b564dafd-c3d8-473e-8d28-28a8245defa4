// 用户类型
export interface User {
  id: number
  username: string
  email?: string
  display_name?: string
  is_anonymous: boolean
  banned: boolean
  muted: boolean
  is_admin: boolean
  can_create_groups: boolean
  privacy_setting: 'public' | 'friends_only' | 'private'
  allow_stranger_messages: boolean
  created_at: string
  last_seen: string
  relationship_status?: 'friend' | 'request_sent' | 'request_received' | 'none'
}

// 消息类型
export interface Message {
  id: number
  content: string
  message_type: 'text' | 'image' | 'questionnaire' | 'questionnaire_response'
  image_url?: string
  image_thumbnail_url?: string
  questionnaire_id?: number
  questionnaire_response_id?: number
  sender_id: number
  sender_username: string
  recipient_id: number
  recipient_username: string
  timestamp: string
  is_recalled: boolean
  reply_to?: number
  parent_message?: {
    id: number
    content: string
    sender_username: string
  }
}

// 聊天链接类型
export interface ChatLink {
  id: number
  code: string
  creator_id: number
  creator_username: string
  note?: string
  is_used: boolean
  expires_at: string
  is_single_use: boolean
  created_at: string
  is_valid: boolean
}

// 邀请码类型
export interface InviteCode {
  id: number
  code: string
  note?: string
  creator_id?: number
  creator_username?: string
  is_used: boolean
  used_by?: number
  used_by_username?: string
  created_at: string
  used_at?: string
}

// 会话类型
export interface Conversation {
  user: User
  last_message: Message
  unread_count: number
}

// API 响应类型
export interface ApiResponse<T = any> {
  message?: string
  error?: string
  data?: T
}

// 分页类型
export interface Pagination {
  page: number
  pages: number
  per_page: number
  total: number
  has_next: boolean
  has_prev: boolean
}

// Socket.IO 事件类型
export interface SocketEvents {
  // 客户端发送的事件
  send_message: (data: {
    content: string
    recipient_id: number
    reply_to?: number
  }) => void
  
  recall_message: (data: { message_id: number }) => void
  
  typing: (data: {
    recipient_id: number
    is_typing: boolean
  }) => void
  
  join_conversation: (data: { user_id: number }) => void
  leave_conversation: (data: { user_id: number }) => void
  get_online_users: () => void
  
  // 服务器发送的事件
  connected: (data: { message: string; user: User }) => void
  new_message: (data: { message: Message }) => void
  new_conversation: (data: { user: User; message: Message }) => void
  message_sent: (data: { message: Message; status: string }) => void
  message_recalled: (data: { message: Message }) => void
  user_typing: (data: {
    user_id: number
    username: string
    is_typing: boolean
  }) => void
  user_online: (data: { user_id: number; username: string }) => void
  user_offline: (data: { user_id: number; username: string }) => void
  online_users: (data: { users: Array<{
    user_id: number
    username: string
    connected_at: string
  }> }) => void
  joined_conversation: (data: { room: string; other_user_id: number }) => void
  left_conversation: (data: { room: string; other_user_id: number }) => void
  error: (data: { message: string }) => void
}

// 表单类型
export interface LoginForm {
  username: string
  password: string
}

export interface RegisterForm {
  username: string
  password: string
  confirmPassword: string
  invite_code?: string
}

export interface MessageForm {
  content: string
  reply_to?: number
}

// 管理员统计类型
export interface AdminStats {
  users: {
    total: number
    anonymous: number
    registered: number
    banned: number
    active_24h: number
  }
  messages: {
    total: number
    last_24h: number
    recalled: number
  }
  links: {
    total: number
    used: number
    active: number
  }
  invites: {
    total: number
    used: number
    available: number
  }
}

// 在线状态类型
export interface OnlineStatus {
  [userId: number]: {
    username: string
    connected_at: string
  }
}

// 输入状态类型
export interface TypingStatus {
  [userId: number]: {
    username: string
    is_typing: boolean
    timeout?: NodeJS.Timeout
  }
}

// 好友请求类型
export interface FriendRequest {
  id: number
  sender_id: number
  sender_username: string
  sender_display_name?: string
  receiver_id: number
  receiver_username: string
  receiver_display_name?: string
  status: 'pending' | 'accepted' | 'declined'
  message?: string
  is_read: boolean
  created_at: string
  updated_at: string
}

// 屏蔽用户类型
export interface BlockedUser {
  id: number
  blocker_id: number
  blocker_username: string
  blocked_id: number
  blocked_username: string
  blocked_display_name?: string
  created_at: string
}

// 群组类型
export interface Group {
  id: number
  name: string
  description?: string
  creator_id: number
  creator_username: string
  is_private: boolean
  max_members: number
  member_count: number
  created_at: string
  updated_at: string
  members?: GroupMembership[]
  statistics?: {
    total_messages: number
    recent_messages_7d: number
  }
}

// 群组成员关系类型
export interface GroupMembership {
  id: number
  group_id: number
  group_name: string
  user_id: number
  username: string
  display_name?: string
  role: 'owner' | 'admin' | 'member'
  joined_at: string
}

// 群组消息类型
export interface GroupMessage {
  id: number
  content: string
  message_type: 'text' | 'image'
  image_url?: string
  image_thumbnail_url?: string
  sender_id: number
  sender_username: string
  sender_display_name?: string
  group_id: number
  group_name: string
  timestamp: string
  is_recalled: boolean
  reply_to?: number
  parent_message?: {
    id: number
    content: string
    sender_username: string
    sender_display_name?: string
  }
}

// 好友管理表单类型
export interface FriendRequestForm {
  receiver_id: number
  message?: string
}

export interface UserSearchForm {
  query: string
}

export interface PrivacySettingsForm {
  privacy_setting: 'public' | 'friends_only' | 'private'
}

// 问卷相关类型
export interface QuestionOption {
  id: number
  question_id: number
  option_text: string
  option_order: number
  created_at: string
}

export interface Question {
  id: number
  page_id: number
  question_text: string
  question_type: 'single_choice' | 'multiple_choice' | 'text_input'
  is_required: boolean
  question_order: number
  created_at: string
  options: QuestionOption[]
}

export interface QuestionnairePage {
  id: number
  questionnaire_id: number
  title: string
  description?: string
  page_order: number
  created_at: string
  questions: Question[]
}

export interface Questionnaire {
  id: number
  title: string
  description?: string
  creator_id: number
  creator_username?: string
  is_active: boolean
  created_at: string
  updated_at: string
  pages: QuestionnairePage[]
}

export interface Answer {
  id: number
  response_id: number
  question_id: number
  selected_option_ids?: string
  selected_options?: QuestionOption[]
  text_answer?: string
  is_hidden: boolean
  is_revealed: boolean
  revealed_at?: string
  revealed_by?: number
  created_at: string
  question?: Question
}

export interface QuestionnaireResponse {
  id: number
  questionnaire_id: number
  respondent_id: number
  sender_id: number
  message_id?: number
  is_completed: boolean
  submitted_at?: string
  created_at: string
  questionnaire?: Questionnaire
  answers: Answer[]
}

export interface RevealRequest {
  id: number
  answer_id: number
  requester_id: number
  status: 'pending' | 'approved' | 'rejected'
  requested_at: string
  processed_at?: string
  answer?: Answer
  requester?: User
}

// 群组管理表单类型
export interface GroupCreateForm {
  name: string
  description?: string
  is_private: boolean
  max_members: number
}

export interface GroupUpdateForm {
  name?: string
  description?: string
  max_members?: number
}

export interface GroupMessageForm {
  content: string
  reply_to?: number
}

// 扩展的Socket.IO事件类型
export interface ExtendedSocketEvents extends SocketEvents {
  // 好友相关事件
  send_friend_request: (data: {
    receiver_id: number
    message?: string
  }) => void

  accept_friend_request: (data: { request_id: number }) => void
  decline_friend_request: (data: { request_id: number }) => void

  friend_request_sent: (data: { request: FriendRequest }) => void
  friend_request_received: (data: { request: FriendRequest }) => void
  friend_request_accepted: (data: { request: FriendRequest }) => void
  friend_request_declined: (data: { request: FriendRequest }) => void
  friend_removed: (data: { user_id: number; username: string; removed_by: string }) => void

  // 群组相关事件
  join_group: (data: { group_id: number }) => void
  leave_group: (data: { group_id: number }) => void

  send_group_message: (data: {
    group_id: number
    content: string
    reply_to?: number
  }) => void

  group_typing: (data: {
    group_id: number
    is_typing: boolean
  }) => void

  group_member_added: (data: {
    group_id: number
    new_member_id: number
  }) => void

  group_member_removed: (data: {
    group_id: number
    removed_member_id: number
  }) => void

  // 群组服务器事件
  joined_group: (data: { group_id: number; room: string }) => void
  left_group: (data: { group_id: number; room: string }) => void
  user_joined_group: (data: {
    user_id: number
    username: string
    group_id: number
  }) => void
  user_left_group: (data: {
    user_id: number
    username: string
    group_id: number
  }) => void

  new_group_message: (data: { message: GroupMessage }) => void
  group_user_typing: (data: {
    user_id: number
    username: string
    group_id: number
    is_typing: boolean
  }) => void

  added_to_group: (data: {
    group: Group
    added_by: string
  }) => void

  removed_from_group: (data: {
    group: Group
    removed_by: string
  }) => void
}
