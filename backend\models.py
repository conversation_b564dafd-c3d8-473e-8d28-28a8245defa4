from datetime import datetime, timedelta, timezone
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import uuid
import secrets
import string
import enum
import json

db = SQLAlchemy()

def get_local_time():
    """获取当前本地时间（带时区信息）"""
    # 获取系统本地时区的当前时间
    return datetime.now()

def get_utc_time():
    """获取UTC时间"""
    return datetime.utcnow()

class User(db.Model):
    """用户模型"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)  # 邮箱地址
    display_name = db.Column(db.String(100), nullable=True)  # 显示名称
    password_hash = db.Column(db.String(255), nullable=True)  # 匿名用户无密码
    is_anonymous = db.Column(db.Boolean, default=False, nullable=False)
    cookie_id = db.Column(db.String(32), unique=True, nullable=True)  # 匿名用户的cookie标识
    banned = db.Column(db.Boolean, default=False, nullable=False)
    muted = db.Column(db.Boolean, default=False, nullable=False)
    is_admin = db.Column(db.Boolean, default=False, nullable=False)
    can_create_groups = db.Column(db.Boolean, default=False, nullable=False)  # 群组创建权限
    privacy_setting = db.Column(db.String(20), default='public', nullable=False)  # public, friends_only, private
    allow_stranger_messages = db.Column(db.Boolean, default=False, nullable=False)  # 是否允许陌生人发送消息
    avatar = db.Column(db.String(255), nullable=True)  # 头像文件名
    created_at = db.Column(db.DateTime, default=get_local_time)
    last_seen = db.Column(db.DateTime, default=get_local_time)

    # 关系
    sent_messages = db.relationship('Message', foreign_keys='Message.sender_id', backref='sender', lazy='dynamic')
    received_messages = db.relationship('Message', foreign_keys='Message.recipient_id', backref='recipient', lazy='dynamic')
    created_links = db.relationship('ChatLink', backref='creator', lazy='dynamic')

    # 好友关系
    sent_friend_requests = db.relationship('FriendRequest', foreign_keys='FriendRequest.sender_id', backref='sender', lazy='dynamic')
    received_friend_requests = db.relationship('FriendRequest', foreign_keys='FriendRequest.receiver_id', backref='receiver', lazy='dynamic')

    # 群组关系
    created_groups = db.relationship('Group', backref='creator', lazy='dynamic')
    group_memberships = db.relationship('GroupMembership', backref='user', lazy='dynamic')
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        if not self.password_hash:
            return False
        return check_password_hash(self.password_hash, password)
    
    @staticmethod
    def generate_anonymous_username():
        """生成匿名用户名"""
        # 生成6位随机字母数字组合
        chars = string.ascii_uppercase + string.digits
        random_id = ''.join(secrets.choice(chars) for _ in range(6))
        return f"Anon_{random_id}"
    
    @staticmethod
    def create_anonymous_user(cookie_id):
        """创建匿名用户"""
        username = User.generate_anonymous_username()
        # 确保用户名唯一
        while User.query.filter_by(username=username).first():
            username = User.generate_anonymous_username()
        
        user = User(
            username=username,
            is_anonymous=True,
            cookie_id=cookie_id
        )
        return user
    
    def get_friends(self):
        """获取好友列表"""
        # 获取已接受的好友请求
        sent_accepted = db.session.query(FriendRequest).filter(
            FriendRequest.sender_id == self.id,
            FriendRequest.status == 'accepted'
        ).all()

        received_accepted = db.session.query(FriendRequest).filter(
            FriendRequest.receiver_id == self.id,
            FriendRequest.status == 'accepted'
        ).all()

        friend_ids = set()
        for req in sent_accepted:
            friend_ids.add(req.receiver_id)
        for req in received_accepted:
            friend_ids.add(req.sender_id)

        return User.query.filter(User.id.in_(friend_ids)).all()

    def get_blocked_users(self):
        """获取被屏蔽的用户列表"""
        blocked_relationships = db.session.query(BlockedUser).filter_by(blocker_id=self.id).all()
        blocked_ids = [rel.blocked_id for rel in blocked_relationships]
        return User.query.filter(User.id.in_(blocked_ids)).all()

    def is_friend_with(self, user_id):
        """检查是否为好友"""
        return db.session.query(FriendRequest).filter(
            ((FriendRequest.sender_id == self.id) & (FriendRequest.receiver_id == user_id)) |
            ((FriendRequest.sender_id == user_id) & (FriendRequest.receiver_id == self.id)),
            FriendRequest.status == 'accepted'
        ).first() is not None

    def is_blocked_by(self, user_id):
        """检查是否被某用户屏蔽"""
        return db.session.query(BlockedUser).filter_by(
            blocker_id=user_id, blocked_id=self.id
        ).first() is not None

    def has_blocked(self, user_id):
        """检查是否屏蔽了某用户"""
        return db.session.query(BlockedUser).filter_by(
            blocker_id=self.id, blocked_id=user_id
        ).first() is not None

    def to_dict(self):
        """转换为字典"""
        avatar_url = None
        if self.avatar:
            avatar_url = f'/static/avatars/{self.avatar}'

        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'display_name': self.display_name,
            'is_anonymous': self.is_anonymous,
            'banned': self.banned,
            'muted': self.muted,
            'is_admin': self.is_admin,
            'can_create_groups': self.can_create_groups,
            'privacy_setting': self.privacy_setting,
            'allow_stranger_messages': self.allow_stranger_messages,
            'avatar': self.avatar,
            'avatar_url': avatar_url,
            'created_at': self.created_at.isoformat(),
            'last_seen': self.last_seen.isoformat()
        }

class Message(db.Model):
    """消息模型 - 用于一对一聊天"""
    __tablename__ = 'messages'

    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    original_content = db.Column(db.Text, nullable=True)  # 存储撤回前的原始内容
    message_type = db.Column(db.String(30), default='text', nullable=False)  # 消息类型: text, image, questionnaire, questionnaire_response
    image_url = db.Column(db.String(500), nullable=True)  # 图片URL
    image_thumbnail_url = db.Column(db.String(500), nullable=True)  # 缩略图URL
    questionnaire_id = db.Column(db.Integer, nullable=True)  # 问卷ID（用于问卷消息）
    questionnaire_response_id = db.Column(db.Integer, nullable=True)  # 问卷回答ID（用于问卷回答消息）
    reveal_request_id = db.Column(db.Integer, nullable=True)  # 揭秘申请ID（用于揭秘申请消息）
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    recipient_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    timestamp = db.Column(db.DateTime, default=get_local_time)
    is_recalled = db.Column(db.Boolean, default=False, nullable=False)
    reply_to = db.Column(db.Integer, db.ForeignKey('messages.id'), nullable=True)

    # 自引用关系
    replies = db.relationship('Message', backref=db.backref('parent', remote_side=[id]), lazy='dynamic')
    
    def recall(self):
        """撤回消息"""
        if not self.is_recalled:
            self.original_content = self.content  # 保存原始内容
            self.is_recalled = True
            self.content = f"{self.sender.username}撤回了一条消息"
    
    def to_dict(self, current_user=None):
        """转换为字典"""
        result = {
            'id': self.id,
            'content': self.content,
            'original_content': self.original_content,
            'message_type': self.message_type,
            'image_url': self.image_url,
            'image_thumbnail_url': self.image_thumbnail_url,
            'questionnaire_id': self.questionnaire_id,
            'questionnaire_response_id': self.questionnaire_response_id,
            'reveal_request_id': self.reveal_request_id,
            'sender_id': self.sender_id,
            'sender_username': self.sender.username,
            'recipient_id': self.recipient_id,
            'recipient_username': self.recipient.username,
            'timestamp': self.timestamp.isoformat(),
            'is_recalled': self.is_recalled,
            'reply_to': self.reply_to
        }
        
        # 如果是回复消息，包含被回复的消息信息
        if self.reply_to and self.parent:
            result['parent_message'] = {
                'id': self.parent.id,
                'content': self.parent.content,
                'sender_username': self.parent.sender.username
            }
        
        return result

class MessageReadStatus(db.Model):
    """消息已读状态模型 - 追踪用户对消息的已读状态"""
    __tablename__ = 'message_read_status'

    id = db.Column(db.Integer, primary_key=True)
    message_id = db.Column(db.Integer, db.ForeignKey('messages.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    read_at = db.Column(db.DateTime, default=get_local_time)

    # 确保同一用户对同一消息只能有一条已读记录
    __table_args__ = (
        db.UniqueConstraint('message_id', 'user_id', name='unique_message_read_status'),
    )

    # 关系
    message = db.relationship('Message', backref='read_statuses')
    user = db.relationship('User', backref='read_messages')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'message_id': self.message_id,
            'user_id': self.user_id,
            'read_at': self.read_at.isoformat()
        }

class ChatLink(db.Model):
    """聊天链接模型"""
    __tablename__ = 'chat_links'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(36), unique=True, nullable=False)  # UUID4
    creator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    note = db.Column(db.String(255), nullable=True)  # 备注信息
    is_used = db.Column(db.Boolean, default=False, nullable=False)
    expires_at = db.Column(db.DateTime, nullable=False)
    is_single_use = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=get_local_time)
    
    @staticmethod
    def create_link(creator_id, expire_hours=24, single_use=True, note=None):
        """创建聊天链接"""
        code = str(uuid.uuid4())
        expires_at = get_local_time() + timedelta(hours=expire_hours)

        link = ChatLink(
            code=code,
            creator_id=creator_id,
            note=note,
            expires_at=expires_at,
            is_single_use=single_use
        )
        return link
    
    def is_valid(self):
        """检查链接是否有效"""
        if self.expires_at < get_local_time():
            return False
        if self.is_single_use and self.is_used:
            return False
        return True
    
    def use_link(self):
        """使用链接"""
        if self.is_single_use:
            self.is_used = True

    def to_dict(self):
        """转换为字典"""
        # 确保创建者信息被加载
        creator_username = 'Unknown'
        try:
            if self.creator:
                creator_username = self.creator.username
        except Exception as e:
            print(f"Error loading creator for ChatLink {self.id}: {e}")
            # 如果关系加载失败，直接查询用户
            try:
                from sqlalchemy.orm import sessionmaker
                creator = User.query.get(self.creator_id)
                if creator:
                    creator_username = creator.username
            except Exception as e2:
                print(f"Error loading creator by ID for ChatLink {self.id}: {e2}")

        return {
            'id': self.id,
            'code': self.code,
            'creator_id': self.creator_id,
            'creator_username': creator_username,
            'note': self.note,
            'is_used': self.is_used,
            'expires_at': self.expires_at.isoformat(),
            'is_single_use': self.is_single_use,
            'created_at': self.created_at.isoformat(),
            'is_valid': self.is_valid()
        }

class InviteCode(db.Model):
    """邀请码模型"""
    __tablename__ = 'invite_codes'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(16), unique=True, nullable=False)
    note = db.Column(db.String(255), nullable=True)  # 备注信息
    creator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # 创建者ID
    is_used = db.Column(db.Boolean, default=False, nullable=False)
    used_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=get_local_time)
    used_at = db.Column(db.DateTime, nullable=True)

    # 关系
    creator = db.relationship('User', foreign_keys=[creator_id], backref='created_invite_codes')
    user = db.relationship('User', foreign_keys=[used_by], backref='used_invite_codes')
    
    @staticmethod
    def generate_code():
        """生成邀请码"""
        chars = string.ascii_uppercase + string.digits
        return ''.join(secrets.choice(chars) for _ in range(8))
    
    @staticmethod
    def create_batch(count, creator_id=None, note=None):
        """批量创建邀请码"""
        codes = []
        for _ in range(count):
            code = InviteCode.generate_code()
            # 确保邀请码唯一
            while InviteCode.query.filter_by(code=code).first():
                code = InviteCode.generate_code()

            invite = InviteCode(code=code, creator_id=creator_id, note=note)
            codes.append(invite)

        return codes
    
    def use_code(self, user_id):
        """使用邀请码"""
        self.is_used = True
        self.used_by = user_id
        self.used_at = get_local_time()
    
    def to_dict(self):
        """转换为字典"""
        result = {
            'id': self.id,
            'code': self.code,
            'note': self.note,
            'creator_id': self.creator_id,
            'is_used': self.is_used,
            'used_by': self.used_by,
            'created_at': self.created_at.isoformat(),
            'used_at': self.used_at.isoformat() if self.used_at else None
        }

        # 添加创建者用户名
        if self.creator:
            result['creator_username'] = self.creator.username

        # 添加使用者用户名
        if self.user:
            result['used_by_username'] = self.user.username

        return result

class FriendRequest(db.Model):
    """好友请求模型"""
    __tablename__ = 'friend_requests'

    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    status = db.Column(db.String(20), default='pending', nullable=False)  # pending, accepted, declined
    message = db.Column(db.Text, nullable=True)  # 可选的请求消息
    created_at = db.Column(db.DateTime, default=get_local_time)
    updated_at = db.Column(db.DateTime, default=get_local_time, onupdate=get_local_time)

    # 确保同一对用户之间只能有一个活跃的好友请求
    __table_args__ = (
        db.UniqueConstraint('sender_id', 'receiver_id', name='unique_friend_request'),
    )

    def accept(self):
        """接受好友请求"""
        self.status = 'accepted'
        self.updated_at = get_local_time()

    def decline(self):
        """拒绝好友请求"""
        self.status = 'declined'
        self.updated_at = get_local_time()

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'sender_id': self.sender_id,
            'sender_username': self.sender.username,
            'sender_display_name': self.sender.display_name,
            'receiver_id': self.receiver_id,
            'receiver_username': self.receiver.username,
            'receiver_display_name': self.receiver.display_name,
            'status': self.status,
            'message': self.message,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class BlockedUser(db.Model):
    """用户屏蔽关系模型"""
    __tablename__ = 'blocked_users'

    id = db.Column(db.Integer, primary_key=True)
    blocker_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)  # 屏蔽者
    blocked_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)  # 被屏蔽者
    created_at = db.Column(db.DateTime, default=get_local_time)

    # 关系
    blocker = db.relationship('User', foreign_keys=[blocker_id], backref='blocked_relationships')
    blocked = db.relationship('User', foreign_keys=[blocked_id], backref='blocked_by_relationships')

    # 确保同一对用户之间只能有一个屏蔽关系
    __table_args__ = (
        db.UniqueConstraint('blocker_id', 'blocked_id', name='unique_block_relationship'),
    )

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'blocker_id': self.blocker_id,
            'blocker_username': self.blocker.username,
            'blocked_id': self.blocked_id,
            'blocked_username': self.blocked.username,
            'blocked_display_name': self.blocked.display_name,
            'created_at': self.created_at.isoformat()
        }

class FriendRequestRejectCount(db.Model):
    """好友申请拒绝次数记录"""
    __tablename__ = 'friend_request_reject_counts'

    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    reject_count = db.Column(db.Integer, default=0, nullable=False)
    last_rejected_at = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=get_local_time)
    updated_at = db.Column(db.DateTime, default=get_local_time, onupdate=get_local_time)

    # 关系
    sender = db.relationship('User', foreign_keys=[sender_id])
    receiver = db.relationship('User', foreign_keys=[receiver_id])

    # 唯一约束：每对用户只能有一条记录
    __table_args__ = (db.UniqueConstraint('sender_id', 'receiver_id', name='unique_sender_receiver_reject'),)

    def increment_reject_count(self):
        """增加拒绝次数"""
        self.reject_count += 1
        self.last_rejected_at = get_local_time()
        self.updated_at = get_local_time()

    def is_blocked(self):
        """检查是否被禁止申请（连续被拒绝3次）"""
        return self.reject_count >= 3

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'sender_id': self.sender_id,
            'receiver_id': self.receiver_id,
            'reject_count': self.reject_count,
            'last_rejected_at': self.last_rejected_at.isoformat() if self.last_rejected_at else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Group(db.Model):
    """群组模型"""
    __tablename__ = 'groups'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    creator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    is_private = db.Column(db.Boolean, default=False, nullable=False)  # 是否为私有群组
    max_members = db.Column(db.Integer, default=50, nullable=False)  # 最大成员数
    created_at = db.Column(db.DateTime, default=get_local_time)
    updated_at = db.Column(db.DateTime, default=get_local_time, onupdate=get_local_time)

    # 关系
    memberships = db.relationship('GroupMembership', backref='group', lazy='dynamic', cascade='all, delete-orphan')
    messages = db.relationship('GroupMessage', backref='group', lazy='dynamic', cascade='all, delete-orphan')

    def get_members(self):
        """获取群组成员"""
        return db.session.query(User).join(GroupMembership).filter(
            GroupMembership.group_id == self.id
        ).all()

    def get_member_count(self):
        """获取成员数量"""
        return self.memberships.count()

    def is_member(self, user_id):
        """检查用户是否为群组成员"""
        return self.memberships.filter_by(user_id=user_id).first() is not None

    def is_admin(self, user_id):
        """检查用户是否为群组管理员"""
        membership = self.memberships.filter_by(user_id=user_id).first()
        return membership and membership.role in ['admin', 'owner']

    def can_join(self):
        """检查是否可以加入群组"""
        return self.get_member_count() < self.max_members

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'creator_id': self.creator_id,
            'creator_username': self.creator.username,
            'is_private': self.is_private,
            'max_members': self.max_members,
            'member_count': self.get_member_count(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class GroupMembership(db.Model):
    """群组成员关系模型"""
    __tablename__ = 'group_memberships'

    id = db.Column(db.Integer, primary_key=True)
    group_id = db.Column(db.Integer, db.ForeignKey('groups.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    role = db.Column(db.String(20), default='member', nullable=False)  # owner, admin, member
    joined_at = db.Column(db.DateTime, default=get_local_time)

    # 确保同一用户在同一群组中只能有一个成员关系
    __table_args__ = (
        db.UniqueConstraint('group_id', 'user_id', name='unique_group_membership'),
    )

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'group_id': self.group_id,
            'group_name': self.group.name,
            'user_id': self.user_id,
            'username': self.user.username,
            'display_name': self.user.display_name,
            'role': self.role,
            'joined_at': self.joined_at.isoformat()
        }

class GroupMessage(db.Model):
    """群组消息模型"""
    __tablename__ = 'group_messages'

    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    original_content = db.Column(db.Text, nullable=True)  # 存储撤回前的原始内容
    message_type = db.Column(db.String(20), default='text', nullable=False)  # 消息类型: text, image
    image_url = db.Column(db.String(500), nullable=True)  # 图片URL
    image_thumbnail_url = db.Column(db.String(500), nullable=True)  # 缩略图URL
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    group_id = db.Column(db.Integer, db.ForeignKey('groups.id'), nullable=False)
    timestamp = db.Column(db.DateTime, default=get_local_time)
    is_recalled = db.Column(db.Boolean, default=False, nullable=False)
    reply_to = db.Column(db.Integer, db.ForeignKey('group_messages.id'), nullable=True)

    # 关系
    sender = db.relationship('User', backref='sent_group_messages')
    replies = db.relationship('GroupMessage', backref=db.backref('parent', remote_side=[id]), lazy='dynamic')

    def recall(self):
        """撤回消息"""
        if not self.is_recalled:
            self.original_content = self.content  # 保存原始内容
            self.is_recalled = True
            self.content = f"{self.sender.username}撤回了一条消息"

    def to_dict(self, current_user=None):
        """转换为字典"""
        result = {
            'id': self.id,
            'content': self.content,
            'original_content': self.original_content,
            'message_type': self.message_type,
            'image_url': self.image_url,
            'image_thumbnail_url': self.image_thumbnail_url,
            'sender_id': self.sender_id,
            'sender_username': self.sender.username,
            'sender_display_name': self.sender.display_name,
            'group_id': self.group_id,
            'group_name': self.group.name,
            'timestamp': self.timestamp.isoformat(),
            'is_recalled': self.is_recalled,
            'reply_to': self.reply_to
        }

        # 如果是回复消息，包含被回复的消息信息
        if self.reply_to and self.parent:
            result['parent_message'] = {
                'id': self.parent.id,
                'content': self.parent.content,
                'sender_username': self.parent.sender.username,
                'sender_display_name': self.parent.sender.display_name
            }

        return result


# 问卷相关模型

class QuestionType(enum.Enum):
    SINGLE_CHOICE = "single_choice"  # 单选
    MULTIPLE_CHOICE = "multiple_choice"  # 多选
    TEXT_INPUT = "text_input"  # 手动输入

class Questionnaire(db.Model):
    """问卷表"""
    __tablename__ = 'questionnaires'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    creator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=get_local_time)
    updated_at = db.Column(db.DateTime, default=get_local_time, onupdate=get_local_time)

    # 关系
    creator = db.relationship("User", backref="created_questionnaires")
    pages = db.relationship("QuestionnairePage", back_populates="questionnaire", cascade="all, delete-orphan", order_by="QuestionnairePage.page_order")
    responses = db.relationship("QuestionnaireResponse", back_populates="questionnaire", cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'creator_id': self.creator_id,
            'creator_username': self.creator.username if self.creator else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'pages': [page.to_dict() for page in self.pages] if self.pages else []
        }

class QuestionnairePage(db.Model):
    """问卷页面表"""
    __tablename__ = 'questionnaire_pages'

    id = db.Column(db.Integer, primary_key=True)
    questionnaire_id = db.Column(db.Integer, db.ForeignKey('questionnaires.id'), nullable=False)
    title = db.Column(db.String(200))
    description = db.Column(db.Text)
    page_order = db.Column(db.Integer, nullable=False)
    created_at = db.Column(db.DateTime, default=get_local_time)

    # 关系
    questionnaire = db.relationship("Questionnaire", back_populates="pages")
    questions = db.relationship("Question", back_populates="page", cascade="all, delete-orphan", order_by="Question.question_order")

    def to_dict(self):
        return {
            'id': self.id,
            'questionnaire_id': self.questionnaire_id,
            'title': self.title,
            'description': self.description,
            'page_order': self.page_order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'questions': [question.to_dict() for question in self.questions] if self.questions else []
        }

class Question(db.Model):
    """题目表"""
    __tablename__ = 'questions'

    id = db.Column(db.Integer, primary_key=True)
    page_id = db.Column(db.Integer, db.ForeignKey('questionnaire_pages.id'), nullable=False)
    question_text = db.Column(db.Text, nullable=False)
    question_type = db.Column(db.String(20), nullable=False)  # 使用字符串而不是Enum
    is_required = db.Column(db.Boolean, default=False)
    question_order = db.Column(db.Integer, nullable=False)
    created_at = db.Column(db.DateTime, default=get_local_time)

    # 关系
    page = db.relationship("QuestionnairePage", back_populates="questions")
    options = db.relationship("QuestionOption", back_populates="question", cascade="all, delete-orphan", order_by="QuestionOption.option_order")
    answers = db.relationship("Answer", back_populates="question", cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'page_id': self.page_id,
            'question_text': self.question_text,
            'question_type': self.question_type,
            'is_required': self.is_required,
            'question_order': self.question_order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'options': [option.to_dict() for option in self.options] if self.options else []
        }

class QuestionOption(db.Model):
    """题目选项表"""
    __tablename__ = 'question_options'

    id = db.Column(db.Integer, primary_key=True)
    question_id = db.Column(db.Integer, db.ForeignKey('questions.id'), nullable=False)
    option_text = db.Column(db.String(500), nullable=False)
    option_order = db.Column(db.Integer, nullable=False)
    created_at = db.Column(db.DateTime, default=get_local_time)

    # 关系
    question = db.relationship("Question", back_populates="options")

    def to_dict(self):
        return {
            'id': self.id,
            'question_id': self.question_id,
            'option_text': self.option_text,
            'option_order': self.option_order,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class QuestionnaireResponse(db.Model):
    """问卷回答表"""
    __tablename__ = 'questionnaire_responses'

    id = db.Column(db.Integer, primary_key=True)
    questionnaire_id = db.Column(db.Integer, db.ForeignKey('questionnaires.id'), nullable=False)
    respondent_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    message_id = db.Column(db.Integer, db.ForeignKey('messages.id'))
    is_completed = db.Column(db.Boolean, default=False)
    submitted_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=get_local_time)

    # 关系
    questionnaire = db.relationship("Questionnaire", back_populates="responses")
    respondent = db.relationship("User", foreign_keys=[respondent_id], backref="questionnaire_responses")
    sender = db.relationship("User", foreign_keys=[sender_id], backref="sent_questionnaires")
    message = db.relationship("Message", backref="questionnaire_response")
    answers = db.relationship("Answer", back_populates="response", cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'questionnaire_id': self.questionnaire_id,
            'respondent_id': self.respondent_id,
            'sender_id': self.sender_id,
            'message_id': self.message_id,
            'is_completed': self.is_completed,
            'submitted_at': self.submitted_at.isoformat() if self.submitted_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'questionnaire': self.questionnaire.to_dict() if self.questionnaire else None,
            'answers': [answer.to_dict() for answer in self.answers] if self.answers else []
        }

class Answer(db.Model):
    """回答表"""
    __tablename__ = 'answers'

    id = db.Column(db.Integer, primary_key=True)
    response_id = db.Column(db.Integer, db.ForeignKey('questionnaire_responses.id'), nullable=False)
    question_id = db.Column(db.Integer, db.ForeignKey('questions.id'), nullable=False)
    selected_option_ids = db.Column(db.Text)
    text_answer = db.Column(db.Text)
    is_hidden = db.Column(db.Boolean, default=False)
    is_revealed = db.Column(db.Boolean, default=False)
    revealed_at = db.Column(db.DateTime)
    revealed_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=get_local_time)

    # 关系
    response = db.relationship("QuestionnaireResponse", back_populates="answers")
    question = db.relationship("Question", back_populates="answers")
    revealer = db.relationship("User", foreign_keys=[revealed_by], backref="revealed_answers")

    def to_dict(self):
        selected_options = []
        if self.selected_option_ids:
            try:
                option_ids = json.loads(self.selected_option_ids)
                selected_options = [opt.to_dict() for opt in self.question.options if opt.id in option_ids]
            except:
                pass

        return {
            'id': self.id,
            'response_id': self.response_id,
            'question_id': self.question_id,
            'selected_option_ids': self.selected_option_ids,
            'selected_options': selected_options,
            'text_answer': self.text_answer,
            'is_hidden': self.is_hidden,
            'is_revealed': self.is_revealed,
            'revealed_at': self.revealed_at.isoformat() if self.revealed_at else None,
            'revealed_by': self.revealed_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'question': self.question.to_dict() if self.question else None
        }

class RevealRequest(db.Model):
    """揭秘申请表"""
    __tablename__ = 'reveal_requests'

    id = db.Column(db.Integer, primary_key=True)
    answer_id = db.Column(db.Integer, db.ForeignKey('answers.id'), nullable=False)
    requester_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    status = db.Column(db.String(20), default='pending')
    requested_at = db.Column(db.DateTime, default=get_local_time)
    processed_at = db.Column(db.DateTime)

    # 关系
    answer = db.relationship("Answer", backref="reveal_requests")
    requester = db.relationship("User", backref="reveal_requests")

    def to_dict(self):
        return {
            'id': self.id,
            'answer_id': self.answer_id,
            'requester_id': self.requester_id,
            'requester_username': self.requester.username if self.requester else None,
            'status': self.status,
            'requested_at': self.requested_at.isoformat() if self.requested_at else None,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'answer': self.answer.to_dict() if self.answer else None
        }
