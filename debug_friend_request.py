#!/usr/bin/env python3
"""
Debug script to test friend request notifications
"""

import requests
import time
import json

# Configuration
API_URL = "http://localhost:5000"

def login_user(username, password):
    """Login and get session cookies"""
    print(f"🔐 Logging in as {username}...")
    
    login_data = {
        "username": username,
        "password": password
    }
    
    response = requests.post(f"{API_URL}/api/auth/login", json=login_data)
    
    if response.status_code == 200:
        print(f"✅ Login successful for {username}")
        return response.cookies
    else:
        print(f"❌ Login failed for {username}: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def search_users(cookies, query=""):
    """Search for users"""
    print(f"🔍 Searching for users with query: '{query}'")
    
    response = requests.get(f"{API_URL}/api/chat/users/search",
                          params={"q": query},
                          cookies=cookies)
    
    if response.status_code == 200:
        users = response.json().get('users', [])
        print(f"✅ Found {len(users)} users")
        return users
    else:
        print(f"❌ Search failed: {response.status_code}")
        return []

def send_friend_request(cookies, receiver_id, message="Debug test friend request"):
    """Send a friend request"""
    print(f"📤 Sending friend request to user ID {receiver_id}...")
    
    request_data = {
        "receiver_id": receiver_id,
        "message": message
    }
    
    response = requests.post(f"{API_URL}/api/friends/requests",
                           json=request_data,
                           cookies=cookies)
    
    print(f"📤 Friend request response: {response.status_code}")
    print(f"📤 Response body: {response.text}")
    
    return response.status_code == 201

def main():
    print("🚀 Starting friend request notification debug test...")
    
    # Using existing users from the system
    sender_username = "test"  # User ID 3
    sender_password = "test123"  # Update this if different

    receiver_username = "admin"  # User ID 1 - the user who should receive the notification
    
    # Login as sender
    sender_cookies = login_user(sender_username, sender_password)
    if not sender_cookies:
        print("❌ Failed to login as sender")
        return
    
    # Search for receiver
    users = search_users(sender_cookies, receiver_username)
    receiver = None
    for user in users:
        if user['username'] == receiver_username:
            receiver = user
            break
    
    if not receiver:
        print(f"❌ Could not find receiver user: {receiver_username}")
        print("Available users:")
        for user in users[:5]:  # Show first 5 users
            print(f"  - {user['username']} (ID: {user['id']})")
        return
    
    print(f"✅ Found receiver: {receiver['username']} (ID: {receiver['id']})")
    
    # Send friend request
    print(f"\n🔔 Sending friend request from {sender_username} to {receiver_username}...")
    print("📝 Check the backend console for Socket.IO emission logs")
    print("📝 Check the frontend console for Socket.IO reception logs")
    
    success = send_friend_request(sender_cookies, receiver['id'], 
                                f"Debug test from {sender_username} at {time.strftime('%H:%M:%S')}")
    
    if success:
        print("✅ Friend request sent successfully!")
        print("🔍 Now check:")
        print("   1. Backend console for Socket.IO emission logs")
        print("   2. Frontend console for Socket.IO reception logs")
        print("   3. Notification icon in the frontend")
    else:
        print("❌ Failed to send friend request")

if __name__ == "__main__":
    main()
