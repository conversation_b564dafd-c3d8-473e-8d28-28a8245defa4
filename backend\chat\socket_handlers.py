from flask import request
from flask_socketio import emit, join_room, leave_room, disconnect
from flask_jwt_extended import decode_token
from datetime import datetime
from sqlalchemy import or_, and_

from models import db, User, Message, get_local_time

# 存储在线用户
online_users = {}

# 全局socketio实例，用于其他模块导入
socketio_instance = None

def register_socket_handlers(socketio):
    """注册Socket.IO事件处理器"""
    global socketio_instance
    socketio_instance = socketio
    """注册Socket.IO事件处理器"""
    
    @socketio.on('connect')
    def handle_connect(auth):
        """处理客户端连接"""
        try:
            # 从cookies中获取JWT token（支持HttpOnly cookies）
            token = None

            # 调试信息
            print(f"Socket connect - auth: {auth}")
            print(f"Socket connect - request.cookies: {dict(request.cookies)}")
            print(f"Socket connect - request.headers: {dict(request.headers)}")

            # 首先尝试从auth参数获取token（向后兼容）
            if auth and auth.get('token'):
                token = auth.get('token')
                print(f"Token from auth: {token[:20]}..." if token else "None")
            else:
                # 从cookies中获取JWT token
                cookies = request.cookies
                token = cookies.get('access_token_cookie')
                print(f"Token from cookies: {token[:20]}..." if token else "None")

            if not token:
                print("No JWT token found in auth or cookies")
                disconnect()
                return False

            # 解码JWT token
            decoded_token = decode_token(token)
            user_id_str = decoded_token['sub']
            user_id = int(user_id_str)  # 转换为整数
            
            # 验证用户
            user = User.query.get(user_id)
            if not user or user.banned:
                print(f"Invalid or banned user: {user_id}")
                disconnect()
                return False
            
            # 更新用户在线状态
            user.last_seen = get_local_time()
            db.session.commit()

            # 记录在线用户
            online_users[request.sid] = {
                'user_id': user_id,
                'username': user.username,
                'connected_at': get_local_time()
            }
            
            # 加入用户专属房间
            room_name = f"user_{user_id}"
            join_room(room_name)

            print(f"🔌 User {user.username} (ID: {user_id}) connected with session {request.sid}")
            print(f"🔌 Joined room: {room_name}")
            print(f"🔌 Total online users: {len(online_users)}")
            
            # 发送连接成功消息
            emit('connected', {
                'message': 'Connected successfully',
                'user': user.to_dict()
            })
            
            # 广播用户上线状态（可选）
            emit('user_online', {
                'user_id': user_id,
                'username': user.username
            }, broadcast=True, include_self=False)
            
        except Exception as e:
            print(f"Connection error: {e}")
            disconnect()
            return False
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """处理客户端断开连接"""
        if request.sid in online_users:
            user_info = online_users[request.sid]
            user_id = user_info['user_id']
            username = user_info['username']
            
            # 离开用户房间
            leave_room(f"user_{user_id}")
            
            # 移除在线用户记录
            del online_users[request.sid]
            
            print(f"User {username} disconnected")
            
            # 广播用户下线状态（可选）
            emit('user_offline', {
                'user_id': user_id,
                'username': username
            }, broadcast=True)
    
    @socketio.on('send_message')
    def handle_send_message(data):
        """处理发送消息"""
        if request.sid not in online_users:
            emit('error', {'message': 'Not authenticated'})
            return
        
        user_info = online_users[request.sid]
        sender_id = user_info['user_id']
        
        try:
            content = data.get('content', '').strip()
            recipient_id = data.get('recipient_id')
            reply_to = data.get('reply_to')
            message_type = data.get('message_type', 'text')
            image_url = data.get('image_url')
            image_thumbnail_url = data.get('image_thumbnail_url')
            questionnaire_id = data.get('questionnaire_id')
            questionnaire_response_id = data.get('questionnaire_response_id')

            # 验证消息内容
            if message_type == 'text':
                if not content or not recipient_id:
                    emit('error', {'message': 'Content and recipient_id are required'})
                    return
                if len(content) > 1000:
                    emit('error', {'message': 'Message too long'})
                    return
            elif message_type == 'image':
                if not image_url or not recipient_id:
                    emit('error', {'message': 'Image URL and recipient_id are required'})
                    return
                # 对于图片消息，content可以为空或包含图片描述
            elif message_type == 'questionnaire':
                if not questionnaire_id or not recipient_id:
                    emit('error', {'message': 'Questionnaire ID and recipient_id are required'})
                    return
                # 对于问卷消息，content可以为空或包含问卷描述
            elif message_type == 'questionnaire_response':
                if not questionnaire_response_id or not recipient_id:
                    emit('error', {'message': 'Questionnaire response ID and recipient_id are required'})
                    return
                # 对于问卷回答消息，content包含完成信息
            else:
                emit('error', {'message': 'Invalid message type'})
                return
            
            # 验证发送者
            sender = User.query.get(sender_id)
            if not sender or sender.banned or sender.muted:
                emit('error', {'message': 'Cannot send message'})
                return
            
            # 验证接收者
            recipient = User.query.get(recipient_id)
            if not recipient:
                emit('error', {'message': 'Recipient not found'})
                return

            # 检查是否还是好友关系
            from models import FriendRequest
            friend_relationship = FriendRequest.query.filter(
                or_(
                    and_(FriendRequest.sender_id == sender_id, FriendRequest.receiver_id == recipient_id),
                    and_(FriendRequest.sender_id == recipient_id, FriendRequest.receiver_id == sender_id)
                ),
                FriendRequest.status == 'accepted'
            ).first()

            if not friend_relationship:
                emit('error', {'message': '无法给对方发送信息：您已被删除好友'})
                return
            
            # 验证回复消息
            if reply_to:
                parent_message = Message.query.get(reply_to)
                if not parent_message:
                    emit('error', {'message': 'Parent message not found'})
                    return
                
                # 确保回复的消息在同一对话中
                if not ((parent_message.sender_id == sender_id and parent_message.recipient_id == recipient_id) or
                        (parent_message.sender_id == recipient_id and parent_message.recipient_id == sender_id)):
                    emit('error', {'message': 'Invalid parent message'})
                    return
            
            # 创建消息
            message = Message(
                content=content if content else '',
                message_type=message_type,
                image_url=image_url,
                image_thumbnail_url=image_thumbnail_url,
                questionnaire_id=questionnaire_id,
                questionnaire_response_id=questionnaire_response_id,
                sender_id=sender_id,
                recipient_id=recipient_id,
                reply_to=reply_to
            )
            
            db.session.add(message)
            db.session.commit()
            
            message_data = message.to_dict()
            
            # 发送给发送者确认
            emit('message_sent', {
                'message': message_data,
                'status': 'sent'
            })

            # 发送给接收者（用户专属房间）
            emit('new_message', {
                'message': message_data
            }, room=f"user_{recipient_id}")

            # 同时发送到对话房间（确保在对话中的用户都能收到）
            conversation_room = f"conversation_{min(sender_id, recipient_id)}_{max(sender_id, recipient_id)}"
            emit('new_message', {
                'message': message_data
            }, room=conversation_room)

            print(f"Message sent from {sender.username} to {recipient.username} (rooms: user_{recipient_id}, {conversation_room})")
            
        except Exception as e:
            db.session.rollback()
            print(f"Error sending message: {e}")
            emit('error', {'message': 'Failed to send message'})
    
    @socketio.on('recall_message')
    def handle_recall_message(data):
        """处理撤回消息"""
        if request.sid not in online_users:
            emit('error', {'message': 'Not authenticated'})
            return

        user_info = online_users[request.sid]
        user_id = user_info['user_id']

        try:
            message_id = data.get('message_id')
            if not message_id:
                emit('error', {'message': 'Message ID is required'})
                return

            message = Message.query.get(message_id)
            if not message:
                emit('error', {'message': 'Message not found'})
                return

            # 获取当前用户信息
            current_user = User.query.get(user_id)
            if not current_user:
                emit('error', {'message': 'User not found'})
                return

            # 检查权限：管理员可以撤回任何消息，普通用户只能撤回自己的消息
            if not current_user.is_admin and message.sender_id != user_id:
                emit('error', {'message': 'Can only recall your own messages'})
                return

            if message.is_recalled:
                emit('error', {'message': 'Message already recalled'})
                return

            # 检查时间限制：普通用户只能撤回1分钟内的消息，管理员无时间限制
            if not current_user.is_admin:
                from datetime import timedelta
                time_limit = timedelta(minutes=1)
                if get_local_time() - message.timestamp > time_limit:
                    emit('error', {'message': 'Message can only be recalled within 1 minute'})
                    return

            # 撤回消息
            message.recall()
            db.session.commit()

            message_data = message.to_dict(current_user)

            # 通知发送者
            emit('message_recalled', {
                'message': message_data
            })

            # 通知接收者
            emit('message_recalled', {
                'message': message_data
            }, room=f"user_{message.recipient_id}")

            print(f"Message {message_id} recalled by user {user_id}")
            
        except Exception as e:
            db.session.rollback()
            print(f"Error recalling message: {e}")
            emit('error', {'message': 'Failed to recall message'})
    
    @socketio.on('typing')
    def handle_typing(data):
        """处理正在输入状态"""
        if request.sid not in online_users:
            return
        
        user_info = online_users[request.sid]
        user_id = user_info['user_id']
        username = user_info['username']
        
        recipient_id = data.get('recipient_id')
        is_typing = data.get('is_typing', False)
        
        if recipient_id:
            # 发送给接收者
            emit('user_typing', {
                'user_id': user_id,
                'username': username,
                'is_typing': is_typing
            }, room=f"user_{recipient_id}")
    
    @socketio.on('join_conversation')
    def handle_join_conversation(data):
        """加入对话房间"""
        if request.sid not in online_users:
            emit('error', {'message': 'Not authenticated'})
            return
        
        user_info = online_users[request.sid]
        user_id = user_info['user_id']
        
        other_user_id = data.get('user_id')
        if not other_user_id:
            emit('error', {'message': 'User ID is required'})
            return
        
        # 创建对话房间名（使用较小的ID在前）
        room_name = f"conversation_{min(user_id, other_user_id)}_{max(user_id, other_user_id)}"
        join_room(room_name)
        
        emit('joined_conversation', {
            'room': room_name,
            'other_user_id': other_user_id
        })
    
    @socketio.on('leave_conversation')
    def handle_leave_conversation(data):
        """离开对话房间"""
        if request.sid not in online_users:
            return
        
        user_info = online_users[request.sid]
        user_id = user_info['user_id']
        
        other_user_id = data.get('user_id')
        if not other_user_id:
            return
        
        room_name = f"conversation_{min(user_id, other_user_id)}_{max(user_id, other_user_id)}"
        leave_room(room_name)
        
        emit('left_conversation', {
            'room': room_name,
            'other_user_id': other_user_id
        })
    
    @socketio.on('get_online_users')
    def handle_get_online_users():
        """获取在线用户列表"""
        if request.sid not in online_users:
            emit('error', {'message': 'Not authenticated'})
            return
        
        # 返回在线用户列表（不包括自己）
        current_user_id = online_users[request.sid]['user_id']
        online_list = []
        
        for sid, user_info in online_users.items():
            if user_info['user_id'] != current_user_id:
                online_list.append({
                    'user_id': user_info['user_id'],
                    'username': user_info['username'],
                    'connected_at': user_info['connected_at'].isoformat()
                })
        
        emit('online_users', {'users': online_list})

    # Friend Request Socket Handlers
    @socketio.on('send_friend_request')
    def handle_send_friend_request(data):
        """处理发送好友请求"""
        if request.sid not in online_users:
            emit('error', {'message': 'Not authenticated'})
            return

        user_info = online_users[request.sid]
        sender_id = user_info['user_id']
        receiver_id = data.get('receiver_id')
        message = data.get('message', '')

        if not receiver_id:
            emit('error', {'message': 'Receiver ID is required'})
            return

        try:
            from models import User, FriendRequest

            # 检查接收者是否存在
            receiver = User.query.get(receiver_id)
            if not receiver:
                emit('error', {'message': 'User not found'})
                return

            # 检查是否已经有好友请求
            existing_request = FriendRequest.query.filter(
                or_(
                    and_(FriendRequest.sender_id == sender_id, FriendRequest.receiver_id == receiver_id),
                    and_(FriendRequest.sender_id == receiver_id, FriendRequest.receiver_id == sender_id)
                )
            ).first()

            if existing_request:
                if existing_request.status == 'accepted':
                    emit('error', {'message': 'Already friends'})
                    return
                elif existing_request.status == 'pending':
                    emit('error', {'message': 'Friend request already pending'})
                    return

            # 如果存在被拒绝的请求，删除它
            if existing_request and existing_request.status == 'declined':
                db.session.delete(existing_request)

            # 创建新的好友请求
            friend_request = FriendRequest(
                sender_id=sender_id,
                receiver_id=receiver_id,
                message=message[:500] if message else None
            )

            db.session.add(friend_request)
            db.session.commit()

            # 发送给发送者确认
            emit('friend_request_sent', {
                'request': friend_request.to_dict()
            })

            # 实时通知接收者
            emit('friend_request_received', {
                'request': friend_request.to_dict()
            }, room=f"user_{receiver_id}")

        except Exception as e:
            db.session.rollback()
            emit('error', {'message': 'Failed to send friend request'})

    @socketio.on('accept_friend_request')
    def handle_accept_friend_request(data):
        """处理接受好友请求"""
        if request.sid not in online_users:
            emit('error', {'message': 'Not authenticated'})
            return

        user_info = online_users[request.sid]
        user_id = user_info['user_id']
        request_id = data.get('request_id')

        if not request_id:
            emit('error', {'message': 'Request ID is required'})
            return

        try:
            from models import FriendRequest

            friend_request = FriendRequest.query.get(request_id)
            if not friend_request:
                emit('error', {'message': 'Friend request not found'})
                return

            if friend_request.receiver_id != user_id:
                emit('error', {'message': 'Not authorized'})
                return

            if friend_request.status != 'pending':
                emit('error', {'message': 'Request is not pending'})
                return

            # 接受好友请求
            friend_request.accept()
            db.session.commit()

            # 获取用户信息
            receiver = User.query.get(user_id)
            sender = User.query.get(friend_request.sender_id)

            if not receiver or not sender:
                emit('error', {'message': 'User not found'})
                return

            # 通知接受者
            emit('friend_request_accepted', {
                'request': friend_request.to_dict()
            })

            # 通知发送者
            emit('friend_request_accepted', {
                'request': friend_request.to_dict()
            }, room=f"user_{friend_request.sender_id}")

            print(f"Friend request accepted via Socket.IO: {sender.username} and {receiver.username} are now friends")

        except Exception as e:
            db.session.rollback()
            emit('error', {'message': 'Failed to accept friend request'})

    @socketio.on('decline_friend_request')
    def handle_decline_friend_request(data):
        """处理拒绝好友请求"""
        if request.sid not in online_users:
            emit('error', {'message': 'Not authenticated'})
            return

        user_info = online_users[request.sid]
        user_id = user_info['user_id']
        request_id = data.get('request_id')

        if not request_id:
            emit('error', {'message': 'Request ID is required'})
            return

        try:
            from models import FriendRequest

            friend_request = FriendRequest.query.get(request_id)
            if not friend_request:
                emit('error', {'message': 'Friend request not found'})
                return

            if friend_request.receiver_id != user_id:
                emit('error', {'message': 'Not authorized'})
                return

            if friend_request.status != 'pending':
                emit('error', {'message': 'Request is not pending'})
                return

            friend_request.decline()
            db.session.commit()

            # 通知接受者
            emit('friend_request_declined', {
                'request': friend_request.to_dict()
            })

            # 通知发送者
            emit('friend_request_declined', {
                'request': friend_request.to_dict()
            }, room=f"user_{friend_request.sender_id}")

        except Exception as e:
            db.session.rollback()
            emit('error', {'message': 'Failed to decline friend request'})

    # Group Socket Handlers
    @socketio.on('join_group')
    def handle_join_group(data):
        """加入群组房间"""
        if request.sid not in online_users:
            emit('error', {'message': 'Not authenticated'})
            return

        user_info = online_users[request.sid]
        user_id = user_info['user_id']
        group_id = data.get('group_id')

        if not group_id:
            emit('error', {'message': 'Group ID is required'})
            return

        try:
            from models import Group

            group = Group.query.get(group_id)
            if not group:
                emit('error', {'message': 'Group not found'})
                return

            # 检查用户是否为群组成员
            if not group.is_member(user_id):
                emit('error', {'message': 'You are not a member of this group'})
                return

            # 加入群组房间
            room_name = f"group_{group_id}"
            join_room(room_name)

            emit('joined_group', {
                'group_id': group_id,
                'room': room_name
            })

            # 通知其他群组成员用户上线
            emit('user_joined_group', {
                'user_id': user_id,
                'username': user_info['username'],
                'group_id': group_id
            }, room=room_name, include_self=False)

        except Exception as e:
            emit('error', {'message': 'Failed to join group'})

    @socketio.on('leave_group')
    def handle_leave_group(data):
        """离开群组房间"""
        if request.sid not in online_users:
            return

        user_info = online_users[request.sid]
        user_id = user_info['user_id']
        group_id = data.get('group_id')

        if not group_id:
            return

        room_name = f"group_{group_id}"
        leave_room(room_name)

        emit('left_group', {
            'group_id': group_id,
            'room': room_name
        })

        # 通知其他群组成员用户离线
        emit('user_left_group', {
            'user_id': user_id,
            'username': user_info['username'],
            'group_id': group_id
        }, room=room_name)

    @socketio.on('send_group_message')
    def handle_send_group_message(data):
        """发送群组消息"""
        if request.sid not in online_users:
            emit('error', {'message': 'Not authenticated'})
            return

        user_info = online_users[request.sid]
        sender_id = user_info['user_id']

        group_id = data.get('group_id')
        content = data.get('content', '').strip()
        reply_to = data.get('reply_to')

        if not group_id or not content:
            emit('error', {'message': 'Group ID and content are required'})
            return

        if len(content) > 2000:
            emit('error', {'message': 'Message too long'})
            return

        try:
            from models import Group, GroupMessage, User

            group = Group.query.get(group_id)
            if not group:
                emit('error', {'message': 'Group not found'})
                return

            # 检查用户是否为群组成员
            if not group.is_member(sender_id):
                emit('error', {'message': 'You are not a member of this group'})
                return

            # 检查用户是否被禁言
            sender = User.query.get(sender_id)
            if sender.muted:
                emit('error', {'message': 'You are muted and cannot send messages'})
                return

            # 如果是回复消息，验证被回复的消息
            if reply_to:
                parent_message = GroupMessage.query.filter_by(
                    id=reply_to,
                    group_id=group_id
                ).first()
                if not parent_message:
                    emit('error', {'message': 'Parent message not found'})
                    return

            # 创建群组消息
            message = GroupMessage(
                content=content,
                sender_id=sender_id,
                group_id=group_id,
                reply_to=reply_to
            )

            db.session.add(message)
            db.session.commit()

            message_data = message.to_dict()

            # 发送给群组所有成员
            emit('new_group_message', {
                'message': message_data
            }, room=f"group_{group_id}")

        except Exception as e:
            db.session.rollback()
            emit('error', {'message': 'Failed to send group message'})

    @socketio.on('group_typing')
    def handle_group_typing(data):
        """处理群组打字状态"""
        if request.sid not in online_users:
            return

        user_info = online_users[request.sid]
        user_id = user_info['user_id']
        group_id = data.get('group_id')
        is_typing = data.get('is_typing', False)

        if not group_id:
            return

        try:
            from models import Group

            group = Group.query.get(group_id)
            if not group or not group.is_member(user_id):
                return

            # 广播打字状态给群组其他成员
            emit('group_user_typing', {
                'user_id': user_id,
                'username': user_info['username'],
                'group_id': group_id,
                'is_typing': is_typing
            }, room=f"group_{group_id}", include_self=False)

        except Exception as e:
            pass  # 静默处理打字状态错误

    @socketio.on('group_member_added')
    def handle_group_member_added(data):
        """处理群组成员添加通知"""
        if request.sid not in online_users:
            return

        user_info = online_users[request.sid]
        user_id = user_info['user_id']
        group_id = data.get('group_id')
        new_member_id = data.get('new_member_id')

        if not group_id or not new_member_id:
            return

        try:
            from models import Group, User

            group = Group.query.get(group_id)
            new_member = User.query.get(new_member_id)

            if not group or not new_member:
                return

            # 检查操作者是否为群组管理员
            if not group.is_admin(user_id):
                return

            # 通知群组所有成员
            emit('group_member_added', {
                'group_id': group_id,
                'new_member': new_member.to_dict(),
                'added_by': user_info['username']
            }, room=f"group_{group_id}")

            # 通知新成员
            emit('added_to_group', {
                'group': group.to_dict(),
                'added_by': user_info['username']
            }, room=f"user_{new_member_id}")

        except Exception as e:
            pass

    @socketio.on('group_member_removed')
    def handle_group_member_removed(data):
        """处理群组成员移除通知"""
        if request.sid not in online_users:
            return

        user_info = online_users[request.sid]
        user_id = user_info['user_id']
        group_id = data.get('group_id')
        removed_member_id = data.get('removed_member_id')

        if not group_id or not removed_member_id:
            return

        try:
            from models import Group, User

            group = Group.query.get(group_id)
            removed_member = User.query.get(removed_member_id)

            if not group or not removed_member:
                return

            # 检查操作者是否为群组管理员
            if not group.is_admin(user_id):
                return

            # 通知群组所有成员
            emit('group_member_removed', {
                'group_id': group_id,
                'removed_member': removed_member.to_dict(),
                'removed_by': user_info['username']
            }, room=f"group_{group_id}")

            # 通知被移除的成员
            emit('removed_from_group', {
                'group': group.to_dict(),
                'removed_by': user_info['username']
            }, room=f"user_{removed_member_id}")

        except Exception as e:
            pass

def get_socketio():
    """获取socketio实例"""
    return socketio_instance
