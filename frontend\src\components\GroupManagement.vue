<template>
  <div class="space-y-6">
    <!-- 创建群组按钮 -->
    <div v-if="canCreateGroups" class="glass rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-medium text-gray-900">创建群组</h3>
          <p class="text-sm text-gray-500">创建一个新的群组来与多人聊天</p>
        </div>
        <button
          @click="showCreateModal = true"
          class="btn-primary"
        >
          创建群组
        </button>
      </div>
    </div>

    <!-- 我的群组 -->
    <div class="glass rounded-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">我的群组</h3>
        <span class="text-sm text-gray-500">{{ groups.length }} 个群组</span>
      </div>

      <div v-if="loading" class="text-center py-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
      </div>

      <div v-else-if="groups.length > 0" class="space-y-3">
        <div
          v-for="group in groups"
          :key="group.id"
          class="flex items-center justify-between p-4 bg-white/50 rounded-lg hover:bg-white/70 transition-colors cursor-pointer"
          @click="selectGroup(group)"
        >
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-900">{{ group.name }}</div>
              <div v-if="group.description" class="text-sm text-gray-500 max-w-xs truncate">{{ group.description }}</div>
              <div class="flex items-center space-x-2 mt-1">
                <span class="text-xs text-gray-400">{{ group.member_count }} 成员</span>
                <span v-if="group.is_private" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                  私有
                </span>
                <span v-else class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                  公开
                </span>
              </div>
            </div>
          </div>
          
          <div class="flex space-x-2">
            <button
              @click.stop="joinGroupChat(group)"
              class="btn-primary text-sm"
            >
              进入聊天
            </button>
            <button
              @click.stop="showGroupSettings(group)"
              class="btn-secondary text-sm"
            >
              设置
            </button>
          </div>
        </div>
      </div>

      <div v-else class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无群组</h3>
        <p class="mt-1 text-sm text-gray-500">
          {{ canCreateGroups ? '创建一个群组开始群聊' : '加入群组开始群聊' }}
        </p>
      </div>
    </div>

    <!-- 创建群组模态框 -->
    <div v-if="showCreateModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="glass rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">创建群组</h3>
          <button
            @click="showCreateModal = false"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <form @submit.prevent="createGroup" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">群组名称</label>
            <input
              v-model="createForm.name"
              type="text"
              required
              maxlength="100"
              placeholder="输入群组名称"
              class="input-primary w-full text-black"
            >
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">群组描述</label>
            <textarea
              v-model="createForm.description"
              maxlength="500"
              placeholder="输入群组描述（可选）"
              rows="3"
              class="input-primary w-full text-black"
            ></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">最大成员数</label>
            <input
              v-model.number="createForm.max_members"
              type="number"
              min="2"
              max="200"
              required
              class="input-primary w-full text-black"
            >
          </div>

          <div class="flex items-center">
            <input
              v-model="createForm.is_private"
              type="checkbox"
              id="is_private"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            >
            <label for="is_private" class="ml-2 text-sm text-gray-700">
              私有群组（需要邀请才能加入）
            </label>
          </div>

          <div class="flex space-x-3 pt-4">
            <button
              type="button"
              @click="showCreateModal = false"
              class="btn-secondary flex-1"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="creating"
              class="btn-primary flex-1"
            >
              {{ creating ? '创建中...' : '创建群组' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 群组设置模态框 -->
    <div v-if="showSettingsModal && selectedGroup" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="glass rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[80vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">群组设置</h3>
          <button
            @click="showSettingsModal = false"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="space-y-6">
          <!-- 群组信息 -->
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-3">群组信息</h4>
            <div class="space-y-3">
              <div>
                <span class="text-sm font-medium text-gray-700">名称：</span>
                <span class="text-sm text-gray-900">{{ selectedGroup.name }}</span>
              </div>
              <div v-if="selectedGroup.description">
                <span class="text-sm font-medium text-gray-700">描述：</span>
                <span class="text-sm text-gray-900">{{ selectedGroup.description }}</span>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-700">成员数：</span>
                <span class="text-sm text-gray-900">{{ selectedGroup.member_count }} / {{ selectedGroup.max_members }}</span>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-700">创建时间：</span>
                <span class="text-sm text-gray-900">{{ formatTime(selectedGroup.created_at) }}</span>
              </div>
            </div>
          </div>

          <!-- 成员列表 -->
          <div v-if="selectedGroup.members">
            <h4 class="text-md font-medium text-gray-900 mb-3">成员列表</h4>
            <div class="space-y-2 max-h-40 overflow-y-auto">
              <div
                v-for="member in selectedGroup.members"
                :key="member.user_id"
                class="flex items-center justify-between p-2 bg-white/50 rounded"
              >
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span class="text-xs font-medium text-primary-600">
                      {{ member.username.charAt(0).toUpperCase() }}
                    </span>
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ member.username }}</div>
                    <div class="text-xs text-gray-500">{{ getRoleText(member.role) }}</div>
                  </div>
                </div>
                <span :class="[
                  'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium',
                  member.role === 'owner' ? 'bg-purple-100 text-purple-800' :
                  member.role === 'admin' ? 'bg-blue-100 text-blue-800' :
                  'bg-gray-100 text-gray-800'
                ]">
                  {{ getRoleText(member.role) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex space-x-3 pt-4 border-t">
            <button
              @click="leaveGroup(selectedGroup.id)"
              class="btn-secondary text-red-600 hover:bg-red-50"
            >
              离开群组
            </button>
            <button
              @click="showSettingsModal = false"
              class="btn-primary flex-1"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { groupsAPI } from '@/services/api'
import { useAuthStore } from '@/stores/auth'
import { formatTime as formatTimeUtil } from '@/utils/time'
import type { Group, GroupCreateForm } from '@/types'

const emit = defineEmits<{
  joinGroupChat: [group: Group]
}>()

const authStore = useAuthStore()

const groups = ref<Group[]>([])
const loading = ref(false)
const creating = ref(false)
const showCreateModal = ref(false)
const showSettingsModal = ref(false)
const selectedGroup = ref<Group | null>(null)

const createForm = ref<GroupCreateForm>({
  name: '',
  description: '',
  is_private: false,
  max_members: 50
})

const canCreateGroups = computed(() => {
  return authStore.user?.can_create_groups || false
})

onMounted(async () => {
  await loadGroups()
})

const loadGroups = async () => {
  loading.value = true
  try {
    const response = await groupsAPI.getUserGroups({})
    groups.value = response.data.groups
  } catch (error) {
    console.error('Failed to load groups:', error)
  } finally {
    loading.value = false
  }
}

const createGroup = async () => {
  creating.value = true
  try {
    await groupsAPI.createGroup(createForm.value)
    showCreateModal.value = false
    resetCreateForm()
    await loadGroups()
  } catch (error) {
    console.error('Failed to create group:', error)
  } finally {
    creating.value = false
  }
}

const selectGroup = (group: Group) => {
  selectedGroup.value = group
}

const joinGroupChat = (group: Group) => {
  emit('joinGroupChat', group)
}

const showGroupSettings = async (group: Group) => {
  try {
    const response = await groupsAPI.getGroupDetails(group.id)
    selectedGroup.value = response.data.group
    showSettingsModal.value = true
  } catch (error) {
    console.error('Failed to load group details:', error)
  }
}

const leaveGroup = async (groupId: number) => {
  if (!confirm('确定要离开此群组吗？')) return
  
  try {
    await groupsAPI.leaveGroup(groupId)
    showSettingsModal.value = false
    await loadGroups()
  } catch (error) {
    console.error('Failed to leave group:', error)
  }
}

const resetCreateForm = () => {
  createForm.value = {
    name: '',
    description: '',
    is_private: false,
    max_members: 50
  }
}

const getRoleText = (role: string) => {
  switch (role) {
    case 'owner': return '群主'
    case 'admin': return '管理员'
    case 'member': return '成员'
    default: return role
  }
}

const formatTime = (timeString: string) => {
  return formatTimeUtil(timeString)
}
</script>

<style scoped>
.input-primary {
  color: black !important;
}
</style>
