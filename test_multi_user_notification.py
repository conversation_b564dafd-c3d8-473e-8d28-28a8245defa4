#!/usr/bin/env python3
"""
测试多用户好友请求通知功能
"""

import requests
import time
import json

# 配置
BASE_URL = "http://localhost:5000"
API_URL = f"{BASE_URL}/api"

def test_multi_user_notification():
    """测试多用户好友请求通知"""
    
    print("🔧 开始测试多用户好友请求通知...")
    
    # 测试用户信息
    users = [
        {
            "username": "admin",
            "password": "admin123"
        },
        {
            "username": "test", 
            "password": "test123"
        }
    ]
    
    # 登录两个用户
    sessions = {}
    
    for user in users:
        print(f"\n📝 登录用户: {user['username']}")
        
        try:
            login_response = requests.post(f"{API_URL}/auth/login", json={
                "username": user["username"],
                "password": user["password"]
            })
            
            if login_response.status_code == 200:
                sessions[user['username']] = {
                    'cookies': login_response.cookies,
                    'user_data': login_response.json()
                }
                print(f"✅ {user['username']} 登录成功")
                print(f"   用户ID: {sessions[user['username']]['user_data']['user']['id']}")
            else:
                print(f"❌ {user['username']} 登录失败: {login_response.text}")
                return
                
        except Exception as e:
            print(f"❌ {user['username']} 登录异常: {e}")
            return
    
    # 等待Socket连接稳定
    print("\n⏳ 等待Socket连接稳定...")
    time.sleep(3)
    
    # admin向test发送好友请求
    sender = 'admin'
    receiver = 'test'
    
    print(f"\n🔔 {sender} 向 {receiver} 发送好友请求...")
    
    try:
        # 获取接收者信息
        receiver_id = sessions[receiver]['user_data']['user']['id']
        sender_cookies = sessions[sender]['cookies']
        
        print(f"   发送者: {sender}")
        print(f"   接收者: {receiver} (ID: {receiver_id})")
        
        # 发送好友请求
        friend_request_data = {
            "receiver_id": receiver_id,
            "message": f"多用户测试好友请求 - {time.strftime('%H:%M:%S')}"
        }
        
        request_response = requests.post(f"{API_URL}/friends/requests", 
                                       json=friend_request_data,
                                       cookies=sender_cookies)
        
        print(f"📤 好友请求响应状态: {request_response.status_code}")
        
        if request_response.status_code == 201:
            response_data = request_response.json()
            print("✅ 好友请求发送成功！")
            print(f"📄 响应数据: {json.dumps(response_data, indent=2)}")
            
            print(f"\n🎯 请检查以下内容:")
            print(f"   1. 后端日志是否显示Socket事件发送")
            print(f"   2. {receiver} 用户的浏览器是否收到通知")
            print(f"   3. 通知图标是否显示红点")
            print(f"   4. 是否有桌面通知、Toast通知等")
            
        else:
            print(f"❌ 好友请求发送失败: {request_response.text}")
            
    except Exception as e:
        print(f"❌ 发送好友请求异常: {e}")
    
    # 等待一段时间观察结果
    print(f"\n⏳ 等待10秒观察通知效果...")
    time.sleep(10)
    
    # 反向测试：test向admin发送好友请求
    sender = 'test'
    receiver = 'admin'
    
    print(f"\n🔔 反向测试: {sender} 向 {receiver} 发送好友请求...")
    
    try:
        receiver_id = sessions[receiver]['user_data']['user']['id']
        sender_cookies = sessions[sender]['cookies']
        
        friend_request_data = {
            "receiver_id": receiver_id,
            "message": f"反向测试好友请求 - {time.strftime('%H:%M:%S')}"
        }
        
        request_response = requests.post(f"{API_URL}/friends/requests", 
                                       json=friend_request_data,
                                       cookies=sender_cookies)
        
        print(f"📤 反向请求响应状态: {request_response.status_code}")
        
        if request_response.status_code == 201:
            print("✅ 反向好友请求发送成功！")
            print(f"🎯 请检查 {receiver} 用户是否收到通知")
        else:
            print(f"❌ 反向好友请求发送失败: {request_response.text}")
            
    except Exception as e:
        print(f"❌ 反向测试异常: {e}")

if __name__ == "__main__":
    test_multi_user_notification()
