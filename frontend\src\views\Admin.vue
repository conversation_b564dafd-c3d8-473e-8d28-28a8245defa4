<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- 顶部导航栏 -->
    <nav class="glass border-b border-white/30 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <RouterLink to="/" class="text-xl font-bold text-gradient">
            1v1 Chat
          </RouterLink>
          <span class="text-sm text-gray-600">管理后台</span>
        </div>
        
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-700">
            {{ authStore.user?.username }} (管理员)
          </span>
          
          <RouterLink to="/chat" class="btn-secondary text-sm">
            返回聊天
          </RouterLink>
          
          <button 
            @click="handleLogout" 
            class="btn-secondary text-sm"
          >
            登出
          </button>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto px-6 py-8">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card-glass">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">总用户数</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats?.users.total || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="card-glass">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">24小时消息</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats?.messages.last_24h || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="card-glass">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">活跃链接</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats?.links.active || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="card-glass">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">可用邀请码</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats?.invites.available || 0 }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 标签页 -->
      <div class="card-glass">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              class="py-4 px-1 border-b-2 font-medium text-sm transition-colors"
              :class="activeTab === tab.id 
                ? 'border-primary-500 text-primary-600' 
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
            >
              {{ tab.name }}
            </button>
          </nav>
        </div>

        <div class="p-6">
          <!-- 用户管理 -->
          <div v-if="activeTab === 'users'" class="space-y-6">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900">用户管理</h3>
              <div class="flex space-x-3">
                <select v-model="userFilter" class="input-primary text-sm">
                  <option value="all">全部用户</option>
                  <option value="registered">注册用户</option>
                  <option value="anonymous">匿名用户</option>
                  <option value="banned">已封禁</option>
                </select>
                <input
                  v-model="userSearch"
                  type="text"
                  placeholder="搜索用户..."
                  class="input-primary text-sm"
                >
              </div>
            </div>

            <!-- 批量操作 -->
            <div v-if="selectedUsers.length > 0" class="p-4 bg-blue-50 rounded-lg">
              <div class="flex items-center justify-between">
                <span class="text-sm text-blue-700">已选择 {{ selectedUsers.length }} 个用户</span>
                <div class="flex space-x-2">
                  <button
                    @click="batchOperation('ban')"
                    class="btn-secondary text-sm text-red-600 hover:bg-red-50"
                  >
                    批量封禁
                  </button>
                  <button
                    @click="batchOperation('unban')"
                    class="btn-secondary text-sm text-green-600 hover:bg-green-50"
                  >
                    批量解封
                  </button>
                  <button
                    @click="batchOperation('mute')"
                    class="btn-secondary text-sm text-yellow-600 hover:bg-yellow-50"
                  >
                    批量禁言
                  </button>
                  <button
                    @click="batchOperation('unmute')"
                    class="btn-secondary text-sm text-blue-600 hover:bg-blue-50"
                  >
                    批量解禁言
                  </button>
                  <button
                    @click="selectedUsers = []"
                    class="btn-secondary text-sm"
                  >
                    取消选择
                  </button>
                </div>
              </div>
            </div>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        :checked="selectedUsers.length === users.length && users.length > 0"
                        @change="toggleAllUsers"
                        class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      >
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">群组权限</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后活跃</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="user in users" :key="user.id">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        :value="user.id"
                        v-model="selectedUsers"
                        :disabled="user.is_admin"
                        class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      >
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                          <span class="text-sm font-medium text-primary-600">
                            {{ user.username.charAt(0).toUpperCase() }}
                          </span>
                        </div>
                        <div class="ml-3">
                          <div class="text-sm font-medium text-gray-900">{{ user.username }}</div>
                          <div class="text-sm text-gray-500">ID: {{ user.id }}</div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                            :class="user.is_anonymous ? 'bg-gray-100 text-gray-800' : 'bg-blue-100 text-blue-800'">
                        {{ user.is_anonymous ? '匿名' : '注册' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex space-x-1">
                        <span v-if="user.banned" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                          已封禁
                        </span>
                        <span v-if="user.muted" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          已禁言
                        </span>
                        <span v-if="user.is_admin" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                          管理员
                        </span>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <label class="flex items-center">
                        <input
                          type="checkbox"
                          :checked="user.can_create_groups"
                          @change="updateGroupPermission(user.id, !user.can_create_groups)"
                          :disabled="user.is_admin || user.is_anonymous"
                          class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        >
                        <span class="ml-2 text-sm text-gray-700">
                          {{ user.can_create_groups ? '可创建' : '不可创建' }}
                        </span>
                      </label>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ formatTime(user.last_seen) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div class="flex space-x-2">
                        <button
                          @click="viewUserDetail(user.id)"
                          class="text-blue-600 hover:text-blue-900 text-sm"
                        >
                          详情
                        </button>
                        <button
                          v-if="!user.banned && !user.is_admin"
                          @click="banUser(user.id)"
                          class="text-red-600 hover:text-red-900 text-sm"
                        >
                          封禁
                        </button>
                        <button
                          v-if="user.banned"
                          @click="unbanUser(user.id)"
                          class="text-green-600 hover:text-green-900 text-sm"
                        >
                          解封
                        </button>
                        <button
                          v-if="!user.muted && !user.is_admin"
                          @click="muteUser(user.id)"
                          class="text-yellow-600 hover:text-yellow-900 text-sm"
                        >
                          禁言
                        </button>
                        <button
                          v-if="user.muted"
                          @click="unmuteUser(user.id)"
                          class="text-blue-600 hover:text-blue-900 text-sm"
                        >
                          解禁言
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 聊天记录管理 -->
          <div v-if="activeTab === 'messages'" class="space-y-6">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900">聊天记录管理</h3>
              <div class="flex space-x-3">
                <button
                  @click="exportMessages"
                  :disabled="loading"
                  class="btn-secondary text-sm"
                >
                  {{ loading ? '导出中...' : '导出记录' }}
                </button>
              </div>
            </div>

            <!-- 筛选条件 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">用户ID</label>
                <input
                  v-model="messageFilters.user_id"
                  type="number"
                  placeholder="用户ID"
                  class="input-primary text-sm"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                <input
                  v-model="messageFilters.start_date"
                  type="date"
                  class="input-primary text-sm"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                <input
                  v-model="messageFilters.end_date"
                  type="date"
                  class="input-primary text-sm"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">搜索内容</label>
                <input
                  v-model="messageFilters.search"
                  type="text"
                  placeholder="搜索消息内容"
                  class="input-primary text-sm"
                >
              </div>
            </div>

            <!-- 消息列表 -->
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发送者</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">接收者</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">内容</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="message in messages" :key="message.id">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ formatTime(message.timestamp) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ message.sender?.username || 'Unknown' }}
                      <span v-if="message.sender?.is_anonymous" class="text-xs text-gray-500">(匿名)</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ message.recipient?.username || 'Unknown' }}
                      <span v-if="message.recipient?.is_anonymous" class="text-xs text-gray-500">(匿名)</span>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                      {{ message.content }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                      <span v-if="message.is_recalled" class="text-red-600">已撤回</span>
                      <span v-else class="text-green-600">正常</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 会话管理 -->
          <div v-if="activeTab === 'conversations'" class="space-y-6">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900">会话管理</h3>
            </div>

            <!-- 会话列表 -->
            <div class="space-y-4">
              <div v-for="conversation in conversations" :key="`${conversation.participants[0]?.id}-${conversation.participants[1]?.id}`"
                   class="p-4 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <div class="flex -space-x-2">
                      <div v-for="participant in conversation.participants" :key="participant?.id"
                           class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center text-sm font-medium text-primary-600">
                        {{ participant?.username?.charAt(0).toUpperCase() }}
                      </div>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900">
                        {{ conversation.participants.map(p => p?.username).join(' ↔ ') }}
                      </p>
                      <p class="text-sm text-gray-500">
                        {{ conversation.message_count }} 条消息 · 最后活动: {{ formatTime(conversation.last_activity) }}
                      </p>
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <button
                      @click="viewConversationMessages(conversation)"
                      class="btn-secondary text-sm"
                    >
                      查看消息
                    </button>
                  </div>
                </div>
                <div class="mt-2 text-sm text-gray-600">
                  最新消息: {{ conversation.latest_message.content }}
                </div>
              </div>
            </div>
          </div>

          <!-- 邀请码管理 -->
          <div v-if="activeTab === 'invites'" class="space-y-6">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900">邀请码管理</h3>
            </div>

            <!-- 生成邀请码表单 -->
            <div class="card-glass p-6">
              <h4 class="text-lg font-medium text-gray-900 mb-4">生成新邀请码</h4>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">数量</label>
                  <select v-model="inviteCount" class="input-primary">
                    <option value="1">1个</option>
                    <option value="5">5个</option>
                    <option value="10">10个</option>
                    <option value="20">20个</option>
                    <option value="50">50个</option>
                  </select>
                </div>
                <div class="md:col-span-2">
                  <label class="block text-sm font-medium text-gray-700 mb-2">备注（可选）</label>
                  <input
                    v-model="inviteNote"
                    type="text"
                    placeholder="为这批邀请码添加备注，例如：新用户注册、活动推广等"
                    class="input-primary"
                    maxlength="255"
                  />
                  <p class="text-xs text-gray-500 mt-1">{{ inviteNote.length }}/255 字符</p>
                </div>
              </div>
              <div class="mt-4 flex justify-end">
                <button
                  @click="generateInvites"
                  :disabled="loading"
                  class="btn-primary"
                >
                  {{ loading ? '生成中...' : '生成邀请码' }}
                </button>
              </div>
            </div>

            <!-- 邀请码列表 -->
            <div class="card-glass">
              <div class="px-6 py-4 border-b border-white/30">
                <h4 class="text-lg font-medium text-gray-900">邀请码列表</h4>
              </div>
              <div class="p-6">
                <div class="text-center py-8">
                  <p class="text-gray-500">邀请码列表功能开发中...</p>
                  <p class="text-gray-400 text-sm mt-2">将显示已生成的邀请码及其使用状态</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 群组管理 -->
          <div v-if="activeTab === 'groups'" class="space-y-6">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900">群组管理</h3>
              <div class="flex space-x-3">
                <input
                  v-model="groupSearch"
                  @input="loadGroups"
                  type="text"
                  placeholder="搜索群组..."
                  class="input-primary text-sm"
                >
              </div>
            </div>

            <div class="bg-white rounded-lg shadow overflow-hidden">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">群组信息</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建者</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成员数</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="group in groups" :key="group.id">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div class="text-sm font-medium text-gray-900">{{ group.name }}</div>
                        <div class="text-sm text-gray-500">{{ group.description || '无描述' }}</div>
                        <div class="flex items-center mt-1">
                          <span v-if="group.is_private" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                            私有
                          </span>
                          <span v-else class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                            公开
                          </span>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ group.creator_username }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ group.member_count }} / {{ group.max_members }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ new Date(group.created_at).toLocaleDateString() }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        @click="deleteGroup(group.id)"
                        class="text-red-600 hover:text-red-900"
                      >
                        删除
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 好友关系管理 -->
          <div v-if="activeTab === 'friends'" class="space-y-6">
            <h3 class="text-lg font-medium text-gray-900">好友关系管理</h3>

            <!-- 好友请求 -->
            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center justify-between mb-4">
                <h4 class="text-md font-medium text-gray-900">好友请求</h4>
                <select v-model="friendRequestFilter" @change="loadFriendRequests" class="input-primary text-sm">
                  <option value="all">全部请求</option>
                  <option value="pending">待处理</option>
                  <option value="accepted">已接受</option>
                  <option value="declined">已拒绝</option>
                </select>
              </div>

              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发送者</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">接收者</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">消息</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="request in friendRequests" :key="request.id">
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ request.sender_username }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ request.receiver_username }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span :class="{
                          'bg-yellow-100 text-yellow-800': request.status === 'pending',
                          'bg-green-100 text-green-800': request.status === 'accepted',
                          'bg-red-100 text-red-800': request.status === 'declined'
                        }" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium">
                          {{ request.status === 'pending' ? '待处理' : request.status === 'accepted' ? '已接受' : '已拒绝' }}
                        </span>
                      </td>
                      <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                        {{ request.message || '无消息' }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ new Date(request.created_at).toLocaleDateString() }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 屏蔽关系 -->
            <div class="bg-white rounded-lg shadow p-6">
              <h4 class="text-md font-medium text-gray-900 mb-4">屏蔽关系</h4>

              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">屏蔽者</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">被屏蔽者</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">屏蔽时间</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="blocked in blockedUsers" :key="blocked.id">
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ blocked.blocker_username }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ blocked.blocked_username }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ new Date(blocked.created_at).toLocaleDateString() }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- 问卷管理 -->
          <div v-if="activeTab === 'questionnaires'" class="space-y-6">
            <QuestionnaireManagement />
          </div>

          <!-- 系统监控 -->
          <div v-if="activeTab === 'monitor'" class="space-y-6">
            <h3 class="text-lg font-medium text-gray-900">系统监控</h3>
            
            <div class="text-center py-8">
              <p class="text-gray-500">系统监控功能开发中...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户详情模态框 -->
    <UserDetailModal
      :show="showUserDetail"
      :user-id="selectedUserId"
      @close="showUserDetail = false"
      @ban-user="banUser"
      @unban-user="unbanUser"
      @mute-user="muteUser"
      @unmute-user="unmuteUser"
      @view-messages="viewUserMessages"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { adminAPI } from '@/services/api'
import type { User, AdminStats } from '@/types'
import UserDetailModal from '@/components/UserDetailModal.vue'
import QuestionnaireManagement from '@/views/admin/QuestionnaireManagement.vue'
import { formatTime } from '@/utils/time'

const router = useRouter()
const authStore = useAuthStore()

const activeTab = ref('users')
const loading = ref(false)
const stats = ref<AdminStats | null>(null)
const users = ref<User[]>([])
const userFilter = ref('all')
const userSearch = ref('')
const inviteCount = ref(10)
const inviteNote = ref('')
const messages = ref<any[]>([])
const conversations = ref<any[]>([])
const messageFilters = ref({
  user_id: '',
  start_date: '',
  end_date: '',
  search: ''
})
const showUserDetail = ref(false)
const selectedUserId = ref<number | null>(null)
const selectedUsers = ref<number[]>([])

// 新增的响应式变量
const groups = ref<any[]>([])
const friendRequests = ref<any[]>([])
const blockedUsers = ref<any[]>([])
const groupSearch = ref('')
const friendRequestFilter = ref('all')

const tabs = [
  { id: 'users', name: '用户管理' },
  { id: 'groups', name: '群组管理' },
  { id: 'friends', name: '好友关系' },
  { id: 'messages', name: '聊天记录' },
  { id: 'conversations', name: '会话管理' },
  { id: 'questionnaires', name: '问卷管理' },
  { id: 'invites', name: '邀请码管理' },
  { id: 'monitor', name: '系统监控' }
]

onMounted(async () => {
  await loadStats()
  await loadUsers()
  if (activeTab.value === 'messages') {
    await loadMessages()
  }
  if (activeTab.value === 'conversations') {
    await loadConversations()
  }
})

// 加载统计信息
const loadStats = async () => {
  try {
    const response = await adminAPI.getStats()
    stats.value = response.data
  } catch (error) {
    console.error('Failed to load stats:', error)
  }
}

// 加载用户列表
const loadUsers = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getUsers({
      type: userFilter.value as any,
      search: userSearch.value
    })
    users.value = response.data.users
  } catch (error) {
    console.error('Failed to load users:', error)
  } finally {
    loading.value = false
  }
}

// 封禁用户
const banUser = async (userId: number) => {
  if (!confirm('确定要封禁此用户吗？')) return
  
  try {
    await adminAPI.banUser(userId)
    await loadUsers()
    await loadStats()
  } catch (error) {
    console.error('Failed to ban user:', error)
  }
}

// 解封用户
const unbanUser = async (userId: number) => {
  try {
    await adminAPI.unbanUser(userId)
    await loadUsers()
    await loadStats()
  } catch (error) {
    console.error('Failed to unban user:', error)
  }
}

// 禁言用户
const muteUser = async (userId: number) => {
  if (!confirm('确定要禁言此用户吗？')) return
  
  try {
    await adminAPI.muteUser(userId)
    await loadUsers()
  } catch (error) {
    console.error('Failed to mute user:', error)
  }
}

// 解除禁言
const unmuteUser = async (userId: number) => {
  try {
    await adminAPI.unmuteUser(userId)
    await loadUsers()
  } catch (error) {
    console.error('Failed to unmute user:', error)
  }
}

// 加载消息列表
const loadMessages = async () => {
  loading.value = true
  try {
    const params: any = {}
    if (messageFilters.value.user_id) params.user_id = parseInt(messageFilters.value.user_id)
    if (messageFilters.value.start_date) params.start_date = messageFilters.value.start_date
    if (messageFilters.value.end_date) params.end_date = messageFilters.value.end_date
    if (messageFilters.value.search) params.search = messageFilters.value.search

    const response = await adminAPI.getMessages(params)
    messages.value = response.data.messages
  } catch (error) {
    console.error('Failed to load messages:', error)
  } finally {
    loading.value = false
  }
}

// 加载会话列表
const loadConversations = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getAllConversations({})
    conversations.value = response.data.conversations
  } catch (error) {
    console.error('Failed to load conversations:', error)
  } finally {
    loading.value = false
  }
}

// 导出消息
const exportMessages = async () => {
  loading.value = true
  try {
    const params: any = { format: 'csv' }
    if (messageFilters.value.user_id) params.user_id = parseInt(messageFilters.value.user_id)
    if (messageFilters.value.start_date) params.start_date = messageFilters.value.start_date
    if (messageFilters.value.end_date) params.end_date = messageFilters.value.end_date

    const response = await adminAPI.exportMessages(params)

    // 创建下载链接
    const blob = new Blob([response.data], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `chat_messages_${new Date().toISOString().slice(0, 10)}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    alert('消息导出成功！')
  } catch (error) {
    console.error('Failed to export messages:', error)
    alert('导出失败')
  } finally {
    loading.value = false
  }
}

// 查看会话消息
const viewConversationMessages = (conversation: any) => {
  const userIds = conversation.participants.map((p: any) => p?.id).filter(Boolean)
  if (userIds.length >= 2) {
    messageFilters.value.user_id = userIds[0].toString()
    activeTab.value = 'messages'
    loadMessages()
  }
}

// 查看用户详情
const viewUserDetail = (userId: number) => {
  selectedUserId.value = userId
  showUserDetail.value = true
}

// 查看用户消息
const viewUserMessages = (userId: number) => {
  showUserDetail.value = false
  messageFilters.value.user_id = userId.toString()
  activeTab.value = 'messages'
  loadMessages()
}

// 切换全选
const toggleAllUsers = () => {
  if (selectedUsers.value.length === users.value.length) {
    selectedUsers.value = []
  } else {
    selectedUsers.value = users.value.filter(u => !u.is_admin).map(u => u.id)
  }
}

// 批量操作
const batchOperation = async (operation: 'ban' | 'unban' | 'mute' | 'unmute' | 'delete') => {
  if (selectedUsers.value.length === 0) return

  const operationNames = {
    ban: '封禁',
    unban: '解封',
    mute: '禁言',
    unmute: '解禁言',
    delete: '删除'
  }

  if (!confirm(`确定要${operationNames[operation]} ${selectedUsers.value.length} 个用户吗？`)) {
    return
  }

  loading.value = true
  try {
    await adminAPI.batchUserOperations({
      user_ids: selectedUsers.value,
      operation
    })

    selectedUsers.value = []
    await loadUsers()
    await loadStats()
    alert(`批量${operationNames[operation]}成功！`)
  } catch (error) {
    console.error('Batch operation failed:', error)
    alert(`批量${operationNames[operation]}失败`)
  } finally {
    loading.value = false
  }
}

// 生成邀请码
const generateInvites = async () => {
  loading.value = true
  try {
    const data = {
      count: inviteCount.value,
      note: inviteNote.value.trim() || undefined
    }
    await adminAPI.generateInvites(data)
    await loadStats()

    const noteText = inviteNote.value.trim() ? `（备注：${inviteNote.value.trim()}）` : ''
    alert(`成功生成 ${inviteCount.value} 个邀请码！${noteText}`)

    // 清空备注输入框
    inviteNote.value = ''
  } catch (error) {
    console.error('Failed to generate invites:', error)
    alert('生成邀请码失败')
  } finally {
    loading.value = false
  }
}

// 登出
const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('Failed to logout:', error)
  }
}



// 监听筛选条件变化
import { watch } from 'vue'

watch([userFilter, userSearch], () => {
  loadUsers()
})

// 加载群组列表
const loadGroups = async () => {
  loading.value = true
  try {
    const response = await adminAPI.getAllGroups({
      search: groupSearch.value
    })
    groups.value = response.data.groups
  } catch (error) {
    console.error('Failed to load groups:', error)
  } finally {
    loading.value = false
  }
}

// 加载好友请求列表
const loadFriendRequests = async () => {
  try {
    const response = await adminAPI.getAllFriendRequests({
      status: friendRequestFilter.value as any
    })
    friendRequests.value = response.data.friend_requests
  } catch (error) {
    console.error('Failed to load friend requests:', error)
  }
}

// 加载屏蔽用户列表
const loadBlockedUsers = async () => {
  try {
    const response = await adminAPI.getAllBlockedRelationships({})
    blockedUsers.value = response.data.blocked_relationships
  } catch (error) {
    console.error('Failed to load blocked users:', error)
  }
}

// 更新用户群组创建权限
const updateGroupPermission = async (userId: number, canCreateGroups: boolean) => {
  try {
    await adminAPI.updateGroupPermission(userId, canCreateGroups)
    await loadUsers() // 重新加载用户列表
  } catch (error) {
    console.error('Failed to update group permission:', error)
  }
}

// 删除群组
const deleteGroup = async (groupId: number) => {
  if (!confirm('确定要删除此群组吗？此操作不可撤销！')) return

  try {
    await adminAPI.adminDeleteGroup(groupId)
    await loadGroups() // 重新加载群组列表
  } catch (error) {
    console.error('Failed to delete group:', error)
  }
}

// 监听标签页变化
watch(activeTab, async (newTab) => {
  if (newTab === 'messages') {
    await loadMessages()
  } else if (newTab === 'conversations') {
    await loadConversations()
  } else if (newTab === 'groups') {
    await loadGroups()
  } else if (newTab === 'friends') {
    await loadFriendRequests()
    await loadBlockedUsers()
  }
})

// 监听消息筛选条件变化
watch(messageFilters, () => {
  if (activeTab.value === 'messages') {
    loadMessages()
  }
}, { deep: true })
</script>
