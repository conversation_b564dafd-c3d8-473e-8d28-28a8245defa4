import os
from flask import Flask, request, jsonify, make_response, send_from_directory
from flask_cors import CORS
from flask_jwt_extended import JWTManager
from flask_socketio import SocketIO
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

from config import config
from models import db, User, Message, ChatLink, InviteCode, MessageReadStatus

def create_app(config_name=None):
    """应用工厂函数"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    # CORS配置 - 开发环境允许更多来源
    allowed_origins = [
        'http://localhost:5173',
        'http://localhost:3000',
        'http://localhost:8080',
        'null'
    ]
    if app.config['DEBUG']:
        # 开发环境允许指定的来源，但不是所有来源（安全考虑）
        CORS(app, supports_credentials=True, origins=allowed_origins)
    else:
        CORS(app, supports_credentials=True, origins=['http://localhost:5173'])
    
    # JWT 配置
    jwt = JWTManager(app)
    # 可以注释掉这些日志来减少输出
    # print(f"JWT Secret Key: {app.config['JWT_SECRET_KEY'][:10]}...")  # 只显示前10个字符
    # print(f"JWT Token Location: {app.config['JWT_TOKEN_LOCATION']}")
    # print(f"JWT Cookie Name: {app.config.get('JWT_ACCESS_COOKIE_NAME', 'default')}")
    
    # Socket.IO 配置
    socketio = SocketIO(
        app,
        cors_allowed_origins="*",
        async_mode=app.config['SOCKETIO_ASYNC_MODE'],
        logger=False,  # 减少日志输出
        engineio_logger=False,  # 减少引擎日志输出
        ping_timeout=60,
        ping_interval=25,
        # 添加更多配置来改善稳定性
        transports=['websocket', 'polling'],
        upgrade=True,
        cookie=None,  # 不使用 Socket.IO 自己的 cookie
        # 添加错误处理配置
        always_connect=False,  # 允许连接被拒绝
        manage_session=True
    )
    
    # 速率限制
    limiter = Limiter(
        key_func=get_remote_address,
        storage_uri=app.config['RATELIMIT_STORAGE_URL']
    )
    limiter.init_app(app)
    
    # 注册蓝图
    from auth import auth_bp
    from chat import chat_bp
    from admin import admin_bp
    from friends import friends_bp
    from groups import groups_bp
    from questionnaire import questionnaire_bp

    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(chat_bp, url_prefix='/api/chat')
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    app.register_blueprint(friends_bp, url_prefix='/api/friends')
    app.register_blueprint(groups_bp, url_prefix='/api/groups')
    app.register_blueprint(questionnaire_bp)

    # 静态文件路由
    @app.route('/static/chat_images/<filename>')
    def chat_images(filename):
        return send_from_directory(os.path.join(app.root_path, 'static', 'chat_images'), filename)

    @app.route('/static/chat_thumbnails/<filename>')
    def chat_thumbnails(filename):
        return send_from_directory(os.path.join(app.root_path, 'static', 'chat_thumbnails'), filename)
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
        print("Database tables created successfully")

        # 修复数据库字段（添加新字段）
        try:
            from sqlalchemy import text

            # 检查并添加 invite_codes 表的新字段
            result = db.session.execute(text("PRAGMA table_info(invite_codes)"))
            invite_columns = [row[1] for row in result.fetchall()]

            if 'note' not in invite_columns:
                print("添加 note 字段到 invite_codes 表...")
                db.session.execute(text("ALTER TABLE invite_codes ADD COLUMN note VARCHAR(255)"))
                print("✓ invite_codes.note 字段添加完成")

            if 'creator_id' not in invite_columns:
                print("添加 creator_id 字段到 invite_codes 表...")
                db.session.execute(text("ALTER TABLE invite_codes ADD COLUMN creator_id INTEGER"))
                print("✓ invite_codes.creator_id 字段添加完成")

            # 检查并添加 chat_links 表的新字段
            result = db.session.execute(text("PRAGMA table_info(chat_links)"))
            chat_columns = [row[1] for row in result.fetchall()]

            if 'note' not in chat_columns:
                print("添加 note 字段到 chat_links 表...")
                db.session.execute(text("ALTER TABLE chat_links ADD COLUMN note VARCHAR(255)"))
                print("✓ chat_links.note 字段添加完成")

            # 检查并添加 messages 表的问卷字段
            result = db.session.execute(text("PRAGMA table_info(messages)"))
            message_columns = [row[1] for row in result.fetchall()]

            if 'questionnaire_id' not in message_columns:
                print("添加 questionnaire_id 字段到 messages 表...")
                db.session.execute(text("ALTER TABLE messages ADD COLUMN questionnaire_id INTEGER"))
                print("✓ messages.questionnaire_id 字段添加完成")

            if 'questionnaire_response_id' not in message_columns:
                print("添加 questionnaire_response_id 字段到 messages 表...")
                db.session.execute(text("ALTER TABLE messages ADD COLUMN questionnaire_response_id INTEGER"))
                print("✓ messages.questionnaire_response_id 字段添加完成")

            # 创建问卷相关表
            print("创建问卷相关表...")

            # 问卷表
            db.session.execute(text("""
                CREATE TABLE IF NOT EXISTS questionnaires (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title VARCHAR(200) NOT NULL,
                    description TEXT,
                    creator_id INTEGER NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (creator_id) REFERENCES users (id)
                )
            """))

            # 问卷页面表
            db.session.execute(text("""
                CREATE TABLE IF NOT EXISTS questionnaire_pages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    questionnaire_id INTEGER NOT NULL,
                    title VARCHAR(200),
                    description TEXT,
                    page_order INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (questionnaire_id) REFERENCES questionnaires (id) ON DELETE CASCADE
                )
            """))

            # 题目表
            db.session.execute(text("""
                CREATE TABLE IF NOT EXISTS questions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    page_id INTEGER NOT NULL,
                    question_text TEXT NOT NULL,
                    question_type VARCHAR(20) NOT NULL,
                    is_required BOOLEAN DEFAULT 0,
                    question_order INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (page_id) REFERENCES questionnaire_pages (id) ON DELETE CASCADE
                )
            """))

            # 题目选项表
            db.session.execute(text("""
                CREATE TABLE IF NOT EXISTS question_options (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    question_id INTEGER NOT NULL,
                    option_text VARCHAR(500) NOT NULL,
                    option_order INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (question_id) REFERENCES questions (id) ON DELETE CASCADE
                )
            """))

            # 问卷回答表
            db.session.execute(text("""
                CREATE TABLE IF NOT EXISTS questionnaire_responses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    questionnaire_id INTEGER NOT NULL,
                    respondent_id INTEGER NOT NULL,
                    sender_id INTEGER NOT NULL,
                    message_id INTEGER,
                    is_completed BOOLEAN DEFAULT 0,
                    submitted_at DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (questionnaire_id) REFERENCES questionnaires (id),
                    FOREIGN KEY (respondent_id) REFERENCES users (id),
                    FOREIGN KEY (sender_id) REFERENCES users (id),
                    FOREIGN KEY (message_id) REFERENCES messages (id)
                )
            """))

            # 回答表
            db.session.execute(text("""
                CREATE TABLE IF NOT EXISTS answers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    response_id INTEGER NOT NULL,
                    question_id INTEGER NOT NULL,
                    selected_option_ids TEXT,
                    text_answer TEXT,
                    is_hidden BOOLEAN DEFAULT 0,
                    is_revealed BOOLEAN DEFAULT 0,
                    revealed_at DATETIME,
                    revealed_by INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (response_id) REFERENCES questionnaire_responses (id) ON DELETE CASCADE,
                    FOREIGN KEY (question_id) REFERENCES questions (id),
                    FOREIGN KEY (revealed_by) REFERENCES users (id)
                )
            """))

            # 揭秘申请表
            db.session.execute(text("""
                CREATE TABLE IF NOT EXISTS reveal_requests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    answer_id INTEGER NOT NULL,
                    requester_id INTEGER NOT NULL,
                    status VARCHAR(20) DEFAULT 'pending',
                    requested_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    processed_at DATETIME,
                    FOREIGN KEY (answer_id) REFERENCES answers (id) ON DELETE CASCADE,
                    FOREIGN KEY (requester_id) REFERENCES users (id)
                )
            """))

            print("✓ 问卷相关表创建完成")

            db.session.commit()
            print("数据库字段修复完成")

        except Exception as e:
            print(f"数据库字段修复失败: {e}")
            db.session.rollback()
        
        # 创建默认管理员用户
        admin = User.query.filter_by(username=app.config['ADMIN_USERNAME']).first()
        if not admin:
            admin = User(
                username=app.config['ADMIN_USERNAME'],
                is_anonymous=False,
                is_admin=True
            )
            admin.set_password(app.config['ADMIN_PASSWORD'])
            db.session.add(admin)
            db.session.commit()
            print(f"Created admin user: {app.config['ADMIN_USERNAME']}")
    
    # 基础路由
    @app.route('/')
    def index():
        return jsonify({
            'message': '1v1 Chat Platform API',
            'version': '1.0.0',
            'status': 'running'
        })
    
    @app.route('/health')
    def health_check():
        return jsonify({'status': 'healthy'})
    
    # 错误处理
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': '页面未找到'}), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return jsonify({'error': '服务器内部错误'}), 500

    # 添加 Socket.IO 错误处理
    @socketio.on_error_default
    def default_error_handler(e):
        print(f"Socket.IO error: {e}")
        import traceback
        traceback.print_exc()
        return False

    # 添加连接错误处理
    @socketio.on_error()
    def error_handler(e):
        print(f"Socket.IO connection error: {e}")
        import traceback
        traceback.print_exc()

    @app.errorhandler(429)
    def ratelimit_handler(e):
        return jsonify({'error': '请求过于频繁，请稍后再试', 'message': str(e.description)}), 429
    
    # JWT 用户加载回调
    @jwt.user_identity_loader
    def user_identity_lookup(user):
        return user.id if hasattr(user, 'id') else user

    @jwt.user_lookup_loader
    def user_lookup_callback(_jwt_header, jwt_data):
        identity = jwt_data["sub"]
        # Convert string identity back to integer
        try:
            user_id = int(identity)
            return User.query.filter_by(id=user_id).one_or_none()
        except (ValueError, TypeError):
            return None

    # JWT 错误处理
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        print(f"JWT expired - header: {jwt_header}, payload: {jwt_payload}")
        return jsonify({'error': '登录已过期，请重新登录'}), 401

    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        print(f"JWT invalid - error: {error}")
        return jsonify({'error': '无效的登录凭证'}), 401

    @jwt.unauthorized_loader
    def missing_token_callback(error):
        print(f"JWT missing - error: {error}")
        return jsonify({'error': '需要登录才能访问'}), 401
    
    # Socket.IO 事件处理
    from chat.socket_handlers import register_socket_handlers
    register_socket_handlers(socketio)
    
    return app, socketio

if __name__ == '__main__':
    try:
        print("Starting application...")
        app, socketio = create_app()
        print("Application created successfully")

        # 开发环境启动
        print("Starting server on port 5000...")
        socketio.run(
            app,
            debug=True,  # 启用调试模式来查看错误
            host='0.0.0.0',
            port=5000,
            allow_unsafe_werkzeug=True
        )
    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()
